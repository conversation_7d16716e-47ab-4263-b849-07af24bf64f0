<?php

/**
 * Unified Judge Management Controller
 * 
 * Provides a single, comprehensive interface for managing judges
 * for both coordinators and administrators
 */
class JudgeManagementController extends Controller {
    private $showModel;
    private $showRoleModel;
    private $userModel;
    private $auth;
    private $db;

    public function __construct() {
        parent::__construct();

        $this->auth = new Auth();
        $this->db = new Database();

        // Require login
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }

        // Initialize models using the model() method
        $this->showModel = $this->model('ShowModel');
        $this->showRoleModel = $this->model('ShowRoleModel');
        $this->userModel = $this->model('UserModel');
    }

    /**
     * Unified judge management dashboard
     */
    public function index($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }

        // Check permissions
        $isAdmin = $this->auth->hasRole('admin');
        $isCoordinator = $this->auth->hasRole('coordinator') && $show->coordinator_id == $this->auth->getCurrentUserId();
        
        if (!$isAdmin && !$isCoordinator) {
            $this->redirect('home/access_denied');
            return;
        }

        // Get comprehensive judge data
        $judgeData = $this->getComprehensiveJudgeData($showId);
        
        $data = [
            'title' => 'Judge Management - ' . $show->name,
            'show' => $show,
            'judge_data' => $judgeData,
            'categories' => $this->showModel->getShowCategories($showId),
            'available_judges' => $this->getAvailableJudges($showId),
            'is_admin' => $isAdmin,
            'is_coordinator' => $isCoordinator,
            'judging_progress' => $this->getJudgingProgress($showId)
        ];

        $this->view('judge_management/dashboard', $data);
    }

    /**
     * Get comprehensive judge data combining role assignments and category assignments
     */
    private function getComprehensiveJudgeData($showId) {
        try {
            // Get judges with role assignments and category assignments
            $this->db->query('
                SELECT DISTINCT
                    u.id as user_id,
                    u.name as judge_name,
                    u.email as judge_email,
                    sra.assigned_role,
                    sra.is_active as role_active,
                    sra.assigned_at as role_assigned_at,
                    GROUP_CONCAT(DISTINCT sc.name ORDER BY sc.name) as assigned_categories,
                    GROUP_CONCAT(DISTINCT ja.category_id ORDER BY ja.category_id) as category_ids,
                    COUNT(DISTINCT ja.id) as category_count,
                    CASE 
                        WHEN sra.id IS NOT NULL AND sra.is_active = 1 THEN "approved"
                        WHEN srr.id IS NOT NULL AND srr.status = "pending" THEN "pending"
                        ELSE "not_assigned"
                    END as status
                FROM users u
                LEFT JOIN show_role_assignments sra ON u.id = sra.user_id 
                    AND sra.show_id = :show_id 
                    AND sra.assigned_role = "judge" 
                    AND sra.is_active = 1
                LEFT JOIN show_role_requests srr ON u.id = srr.user_id 
                    AND srr.show_id = :show_id2 
                    AND srr.requested_role = "judge" 
                    AND srr.status = "pending"
                LEFT JOIN judge_assignments ja ON u.id = ja.judge_id AND ja.show_id = :show_id3
                LEFT JOIN show_categories sc ON ja.category_id = sc.id
                WHERE (sra.id IS NOT NULL OR srr.id IS NOT NULL OR ja.id IS NOT NULL)
                    AND u.role IN ("judge", "admin", "coordinator")
                GROUP BY u.id, u.name, u.email, sra.assigned_role, sra.is_active, sra.assigned_at
                ORDER BY u.name
            ');
            
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':show_id2', $showId);
            $this->db->bind(':show_id3', $showId);
            
            return $this->db->resultSet();
            
        } catch (Exception $e) {
            error_log("JudgeManagementController::getComprehensiveJudgeData - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get available judges who can be assigned
     */
    private function getAvailableJudges($showId) {
        try {
            $this->db->query('
                SELECT u.id, u.name, u.email, u.role
                FROM users u
                WHERE u.role IN ("judge", "admin", "coordinator")
                    AND u.status = "active"
                    AND u.id NOT IN (
                        SELECT sra.user_id 
                        FROM show_role_assignments sra 
                        WHERE sra.show_id = :show_id 
                            AND sra.assigned_role = "judge" 
                            AND sra.is_active = 1
                    )
                ORDER BY u.name
            ');
            
            $this->db->bind(':show_id', $showId);
            return $this->db->resultSet();
            
        } catch (Exception $e) {
            error_log("JudgeManagementController::getAvailableJudges - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get individual judge progress statistics
     */
    private function getJudgingProgress($showId) {
        try {
            // Get judge progress - how many vehicles each judge has completed vs assigned
            $this->db->query('
                SELECT
                    ja.judge_id,
                    u.name as judge_name,
                    ja.category_id,
                    sc.name as category_name,
                    COUNT(DISTINCT r.id) as vehicles_to_judge,
                    COUNT(DISTINCT CASE WHEN s.id IS NOT NULL AND s.is_draft = 0 THEN r.id END) as vehicles_judged,
                    COUNT(DISTINCT CASE WHEN s.id IS NOT NULL AND s.is_draft = 1 THEN r.id END) as vehicles_draft
                FROM judge_assignments ja
                JOIN users u ON ja.judge_id = u.id
                LEFT JOIN show_categories sc ON ja.category_id = sc.id
                LEFT JOIN registrations r ON (ja.category_id IS NULL OR r.category_id = ja.category_id)
                    AND r.show_id = :show_id AND r.status = "approved"
                LEFT JOIN scores s ON r.id = s.registration_id AND s.judge_id = ja.judge_id
                WHERE ja.show_id = :show_id2
                GROUP BY ja.judge_id, ja.category_id, u.name, sc.name
                ORDER BY u.name, sc.name
            ');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':show_id2', $showId);
            $judgeProgress = $this->db->resultSet();

            // Group by judge
            $progressByJudge = [];
            foreach ($judgeProgress as $progress) {
                $judgeId = $progress->judge_id;
                if (!isset($progressByJudge[$judgeId])) {
                    $progressByJudge[$judgeId] = [
                        'judge_name' => $progress->judge_name,
                        'total_to_judge' => 0,
                        'total_judged' => 0,
                        'total_draft' => 0,
                        'categories' => []
                    ];
                }

                $progressByJudge[$judgeId]['total_to_judge'] += $progress->vehicles_to_judge;
                $progressByJudge[$judgeId]['total_judged'] += $progress->vehicles_judged;
                $progressByJudge[$judgeId]['total_draft'] += $progress->vehicles_draft;

                $progressByJudge[$judgeId]['categories'][] = [
                    'category_name' => $progress->category_name ?: 'All Categories',
                    'vehicles_to_judge' => $progress->vehicles_to_judge,
                    'vehicles_judged' => $progress->vehicles_judged,
                    'vehicles_draft' => $progress->vehicles_draft
                ];
            }

            return $progressByJudge;

        } catch (Exception $e) {
            error_log("JudgeManagementController::getJudgingProgress - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Assign judge role and categories in one action
     */
    public function assignJudge() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('home/error');
            return;
        }

        $showId = (int)($_POST['show_id'] ?? 0);
        $judgeId = (int)($_POST['judge_id'] ?? 0);
        $categoryIds = $_POST['category_ids'] ?? [];
        $assignmentType = $_POST['assignment_type'] ?? 'direct'; // direct or request

        if ($showId <= 0 || $judgeId <= 0) {
            $this->setFlashMessage('error', 'Invalid input data', 'danger');
            $this->redirect('judge_management/index/' . $showId);
            return;
        }

        // Check permissions
        $show = $this->showModel->getShowById($showId);
        $isAdmin = $this->auth->hasRole('admin');
        $isCoordinator = $this->auth->hasRole('coordinator') && $show->coordinator_id == $this->auth->getCurrentUserId();
        
        if (!$isAdmin && !$isCoordinator) {
            $this->redirect('home/access_denied');
            return;
        }

        try {
            $this->db->beginTransaction();

            // Step 1: Assign judge role
            if ($isAdmin || $assignmentType === 'direct') {
                // Direct assignment
                $roleResult = $this->showRoleModel->createDirectAssignment(
                    $showId,
                    $judgeId,
                    'judge',
                    $this->auth->getCurrentUserId()
                );
            } else {
                // Request assignment (coordinator)
                $roleResult = $this->showRoleModel->requestRoleAssignment(
                    $showId,
                    $judgeId,
                    'judge',
                    $this->auth->getCurrentUserId(),
                    'Judge assignment for ' . $show->name
                );
            }

            if (!$roleResult) {
                throw new Exception('Failed to assign judge role');
            }

            // Step 2: Assign categories (only if direct assignment)
            if ($isAdmin || $assignmentType === 'direct') {
                foreach ($categoryIds as $categoryId) {
                    $categoryId = (int)$categoryId;
                    if ($categoryId > 0) {
                        $this->showModel->assignJudge([
                            'show_id' => $showId,
                            'judge_id' => $judgeId,
                            'category_id' => $categoryId
                        ]);
                    }
                }

                // If no categories selected, assign to all categories
                if (empty($categoryIds)) {
                    $this->showModel->assignJudge([
                        'show_id' => $showId,
                        'judge_id' => $judgeId,
                        'category_id' => null // All categories
                    ]);
                }
            }

            $this->db->commit();

            $message = $assignmentType === 'direct' ? 'Judge assigned successfully' : 'Judge assignment request sent';
            $this->setFlashMessage('success', $message, 'success');

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("JudgeManagementController::assignJudge - Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to assign judge', 'danger');
        }

        $this->redirect('judge_management/index/' . $showId);
    }

    /**
     * Update judge category assignments
     */
    public function updateCategories() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('home/error');
            return;
        }

        $showId = (int)($_POST['show_id'] ?? 0);
        $judgeId = (int)($_POST['judge_id'] ?? 0);
        $categoryIds = $_POST['category_ids'] ?? [];

        if ($showId <= 0 || $judgeId <= 0) {
            $this->setFlashMessage('error', 'Invalid input data', 'danger');
            $this->redirect('judge_management/index/' . $showId);
            return;
        }

        try {
            // Remove existing category assignments
            $this->db->query('DELETE FROM judge_assignments WHERE show_id = :show_id AND judge_id = :judge_id');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':judge_id', $judgeId);
            $this->db->execute();

            // Add new category assignments
            foreach ($categoryIds as $categoryId) {
                $categoryId = (int)$categoryId;
                if ($categoryId > 0) {
                    $this->showModel->assignJudge([
                        'show_id' => $showId,
                        'judge_id' => $judgeId,
                        'category_id' => $categoryId
                    ]);
                }
            }

            // If no categories selected, assign to all categories
            if (empty($categoryIds)) {
                $this->showModel->assignJudge([
                    'show_id' => $showId,
                    'judge_id' => $judgeId,
                    'category_id' => null // All categories
                ]);
            }

            $this->setFlashMessage('success', 'Judge categories updated successfully', 'success');

        } catch (Exception $e) {
            error_log("JudgeManagementController::updateCategories - Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to update judge categories', 'danger');
        }

        $this->redirect('judge_management/index/' . $showId);
    }

    /**
     * Remove judge assignment
     */
    public function removeJudge($showId, $judgeId) {
        $showId = (int)$showId;
        $judgeId = (int)$judgeId;

        if ($showId <= 0 || $judgeId <= 0) {
            $this->redirect('home/error');
            return;
        }

        // Check permissions
        $show = $this->showModel->getShowById($showId);
        $isAdmin = $this->auth->hasRole('admin');
        $isCoordinator = $this->auth->hasRole('coordinator') && $show->coordinator_id == $this->auth->getCurrentUserId();

        if (!$isAdmin && !$isCoordinator) {
            $this->redirect('home/access_denied');
            return;
        }

        try {
            $this->db->beginTransaction();

            // Remove category assignments
            $this->db->query('DELETE FROM judge_assignments WHERE show_id = :show_id AND judge_id = :judge_id');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':judge_id', $judgeId);
            $this->db->execute();

            // Remove role assignment
            $this->db->query('UPDATE show_role_assignments SET is_active = 0
                              WHERE show_id = :show_id AND user_id = :user_id AND assigned_role = "judge"');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $judgeId);
            $this->db->execute();

            $this->db->commit();
            $this->setFlashMessage('success', 'Judge removed successfully', 'success');

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("JudgeManagementController::removeJudge - Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to remove judge', 'danger');
        }

        $this->redirect('judge_management/index/' . $showId);
    }

    /**
     * Get judge details for editing (AJAX)
     */
    public function getJudgeDetails($showId, $judgeId) {
        // Only allow AJAX requests
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request']);
            return;
        }

        try {
            // Get judge's current category assignments
            $this->db->query('
                SELECT ja.category_id, sc.name as category_name
                FROM judge_assignments ja
                LEFT JOIN show_categories sc ON ja.category_id = sc.id
                WHERE ja.show_id = :show_id AND ja.judge_id = :judge_id
            ');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':judge_id', $judgeId);
            $assignments = $this->db->resultSet();

            // Get judge info
            $this->db->query('SELECT name, email FROM users WHERE id = :judge_id');
            $this->db->bind(':judge_id', $judgeId);
            $judge = $this->db->single();

            echo json_encode([
                'success' => true,
                'judge' => $judge,
                'assignments' => $assignments
            ]);

        } catch (Exception $e) {
            error_log("JudgeManagementController::getJudgeDetails - Error: " . $e->getMessage());
            echo json_encode(['error' => 'Failed to get judge details']);
        }
    }

    /**
     * Send message to judge
     */
    public function sendMessage() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('home/error');
            return;
        }

        $showId = (int)($_POST['show_id'] ?? 0);
        $judgeId = (int)($_POST['judge_id'] ?? 0);
        $message = trim($_POST['message'] ?? '');
        $subject = trim($_POST['subject'] ?? '');

        if ($showId <= 0 || $judgeId <= 0 || empty($message)) {
            $this->setFlashMessage('error', 'Please fill in all required fields', 'danger');
            $this->redirect('judge_management/index/' . $showId);
            return;
        }

        // Check permissions
        $show = $this->showModel->getShowById($showId);
        $isAdmin = $this->auth->hasRole('admin');
        $isCoordinator = $this->auth->hasRole('coordinator') && $show->coordinator_id == $this->auth->getCurrentUserId();

        if (!$isAdmin && !$isCoordinator) {
            $this->redirect('home/access_denied');
            return;
        }

        try {
            // Get judge details
            $this->db->query('SELECT name, email FROM users WHERE id = :judge_id');
            $this->db->bind(':judge_id', $judgeId);
            $judge = $this->db->single();

            // Get sender details
            $senderId = $this->auth->getCurrentUserId();
            $this->db->query('SELECT name, email FROM users WHERE id = :sender_id');
            $this->db->bind(':sender_id', $senderId);
            $sender = $this->db->single();

            if (!$judge || !$sender) {
                throw new Exception('Judge or sender not found');
            }

            // Prepare email
            $emailSubject = $subject ?: "Message from {$sender->name} regarding {$show->name}";
            $emailMessage = "Hello {$judge->name},\n\n";
            $emailMessage .= "You have received a message from {$sender->name} regarding the show '{$show->name}':\n\n";
            $emailMessage .= "---\n";
            $emailMessage .= $message . "\n";
            $emailMessage .= "---\n\n";
            $emailMessage .= "You can reply to this message by contacting {$sender->name} at {$sender->email}.\n\n";
            $emailMessage .= "Thank you!";

            // Send email (using your existing notification system)
            $notificationData = [
                'type' => 'judge_message',
                'show_id' => $showId,
                'sender_id' => $senderId,
                'subject' => $emailSubject
            ];

            $result = $this->showRoleModel->queueNotification($judgeId, 'email', $emailSubject, $emailMessage, $notificationData);

            if ($result) {
                $this->setFlashMessage('success', 'Message sent successfully to ' . $judge->name, 'success');
            } else {
                $this->setFlashMessage('error', 'Failed to send message', 'danger');
            }

        } catch (Exception $e) {
            error_log("JudgeManagementController::sendMessage - Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to send message', 'danger');
        }

        $this->redirect('judge_management/index/' . $showId);
    }
}
