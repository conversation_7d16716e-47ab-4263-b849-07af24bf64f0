<?php

/**
 * Unified Judge Management Controller
 * 
 * Provides a single, comprehensive interface for managing judges
 * for both coordinators and administrators
 */
class JudgeManagementController extends Controller {
    private $showModel;
    private $showRoleModel;
    private $userModel;
    private $judgingModel;
    private $auth;
    private $db;

    public function __construct() {
        parent::__construct();

        $this->auth = new Auth();
        $this->db = new Database();

        // Require login
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }

        // Initialize models using the model() method
        $this->showModel = $this->model('ShowModel');
        $this->showRoleModel = $this->model('ShowRoleModel');
        $this->userModel = $this->model('UserModel');
        $this->judgingModel = $this->model('JudgingModel');
    }

    /**
     * Unified judge management dashboard
     */
    public function index($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }

        // Check permissions
        $isAdmin = $this->auth->hasRole('admin');
        $isCoordinator = $this->auth->hasRole('coordinator') && $show->coordinator_id == $this->auth->getCurrentUserId();
        
        if (!$isAdmin && !$isCoordinator) {
            $this->redirect('home/access_denied');
            return;
        }

        // Get comprehensive judge data
        $judgeData = $this->getComprehensiveJudgeData($showId);
        
        $data = [
            'title' => 'Judge Management - ' . $show->name,
            'show' => $show,
            'judge_data' => $judgeData,
            'categories' => $this->showModel->getShowCategories($showId),
            'available_judges' => $this->getAvailableJudges($showId),
            'is_admin' => $isAdmin,
            'is_coordinator' => $isCoordinator,
            'judging_progress' => $this->getJudgingProgress($showId)
        ];

        $this->view('judge_management/dashboard', $data);
    }

    /**
     * Get comprehensive judge data combining role assignments and category assignments
     */
    private function getComprehensiveJudgeData($showId) {
        try {
            // Get judges using the existing system's tables: judges and judge_categories
            // Only show ACTIVE judges to prevent ghost entries after deletion
            $this->db->query('
                SELECT DISTINCT
                    u.id as user_id,
                    u.name as judge_name,
                    u.email as judge_email,
                    u.phone as judge_phone,
                    j.is_active as role_active,
                    j.created_at as role_assigned_at,
                    j.denial_note,
                    GROUP_CONCAT(DISTINCT sc.name ORDER BY sc.name) as assigned_categories,
                    GROUP_CONCAT(DISTINCT jc.category_id ORDER BY jc.category_id) as category_ids,
                    COUNT(DISTINCT jc.id) as category_count,
                    CASE
                        WHEN j.id IS NOT NULL AND j.is_active = 1 THEN "approved"
                        WHEN j.id IS NOT NULL AND j.is_active = 0 THEN "pending"
                        WHEN j.id IS NOT NULL AND j.is_active = -1 THEN "denied"
                        ELSE "not_assigned"
                    END as status
                FROM users u
                INNER JOIN judges j ON u.id = j.user_id AND j.show_id = :show_id
                LEFT JOIN judge_categories jc ON j.id = jc.judge_id
                LEFT JOIN show_categories sc ON jc.category_id = sc.id
                WHERE u.role IN ("judge", "admin", "coordinator")
                GROUP BY u.id, u.name, u.email, u.phone, j.is_active, j.created_at, j.denial_note
                ORDER BY u.name
            ');
            
            $this->db->bind(':show_id', $showId);

            return $this->db->resultSet();
            
        } catch (Exception $e) {
            error_log("JudgeManagementController::getComprehensiveJudgeData - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get available judges who can be assigned
     */
    private function getAvailableJudges($showId) {
        try {
            // Check permissions
            $show = $this->showModel->getShowById($showId);
            $isAdmin = $this->auth->hasRole('admin');
            $isCoordinator = $this->auth->hasRole('coordinator') && $show->coordinator_id == $this->auth->getCurrentUserId();

            // For coordinators, return empty array - they must use user ID lookup for security
            if ($isCoordinator && !$isAdmin) {
                return [];
            }

            // For admins, only show users not already assigned as judges
            $this->db->query('
                SELECT u.id, u.name, u.email, u.phone, u.role
                FROM users u
                WHERE u.role IN ("judge", "admin", "coordinator")
                    AND u.status = "active"
                    AND u.id NOT IN (
                        SELECT j.user_id
                        FROM judges j
                        WHERE j.show_id = :show_id
                            AND j.is_active = 1
                    )
                ORDER BY u.name
            ');
            
            $this->db->bind(':show_id', $showId);
            return $this->db->resultSet();
            
        } catch (Exception $e) {
            error_log("JudgeManagementController::getAvailableJudges - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get individual judge progress statistics using existing system tables
     */
    private function getJudgingProgress($showId) {
        try {
            // Get judge progress using existing judges and judge_categories tables
            $this->db->query('
                SELECT
                    j.user_id as judge_id,
                    u.name as judge_name,
                    jc.category_id,
                    sc.name as category_name,
                    COUNT(DISTINCT r.id) as vehicles_to_judge,
                    COUNT(DISTINCT CASE WHEN s.id IS NOT NULL AND s.is_draft = 0 THEN r.id END) as vehicles_judged,
                    COUNT(DISTINCT CASE WHEN s.id IS NOT NULL AND s.is_draft = 1 THEN r.id END) as vehicles_draft
                FROM judges j
                JOIN users u ON j.user_id = u.id
                LEFT JOIN judge_categories jc ON j.id = jc.judge_id
                LEFT JOIN show_categories sc ON jc.category_id = sc.id
                LEFT JOIN registrations r ON (jc.category_id IS NULL OR r.category_id = jc.category_id)
                    AND r.show_id = :show_id AND r.status = "approved"
                LEFT JOIN scores s ON r.id = s.registration_id AND s.judge_id = j.user_id
                WHERE j.show_id = :show_id2 AND j.is_active = 1
                GROUP BY j.user_id, jc.category_id, u.name, sc.name
                ORDER BY u.name, sc.name
            ');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':show_id2', $showId);
            $judgeProgress = $this->db->resultSet();

            // Group by judge
            $progressByJudge = [];
            foreach ($judgeProgress as $progress) {
                $judgeId = $progress->judge_id;
                if (!isset($progressByJudge[$judgeId])) {
                    $progressByJudge[$judgeId] = [
                        'judge_name' => $progress->judge_name,
                        'total_to_judge' => 0,
                        'total_judged' => 0,
                        'total_draft' => 0,
                        'categories' => []
                    ];
                }

                // Add to totals (existing system handles categories properly)
                $progressByJudge[$judgeId]['total_to_judge'] += $progress->vehicles_to_judge;
                $progressByJudge[$judgeId]['total_judged'] += $progress->vehicles_judged;
                $progressByJudge[$judgeId]['total_draft'] += $progress->vehicles_draft;

                $progressByJudge[$judgeId]['categories'][] = [
                    'category_name' => $progress->category_name ?: 'All Categories',
                    'vehicles_to_judge' => $progress->vehicles_to_judge,
                    'vehicles_judged' => $progress->vehicles_judged,
                    'vehicles_draft' => $progress->vehicles_draft
                ];
            }

            return $progressByJudge;

        } catch (Exception $e) {
            error_log("JudgeManagementController::getJudgingProgress - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Assign judge role and categories in one action
     */
    public function assignJudge() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('home/error');
            return;
        }

        $showId = (int)($_POST['show_id'] ?? 0);
        $judgeId = (int)($_POST['judge_id'] ?? 0);
        $categoryIds = $_POST['category_ids'] ?? [];
        $assignmentType = $_POST['assignment_type'] ?? 'direct'; // direct or request

        if ($showId <= 0 || $judgeId <= 0) {
            $this->setFlashMessage('error', 'Invalid input data', 'danger');
            $this->redirect('judge_management/index/' . $showId);
            return;
        }

        // Check permissions
        $show = $this->showModel->getShowById($showId);
        $isAdmin = $this->auth->hasRole('admin');
        $isCoordinator = $this->auth->hasRole('coordinator') && $show->coordinator_id == $this->auth->getCurrentUserId();
        
        if (!$isAdmin && !$isCoordinator) {
            $this->redirect('home/access_denied');
            return;
        }

        try {
            $this->db->beginTransaction();

            // Step 1: Verify user exists and get their info
            $this->db->query('SELECT id, name, email, role FROM users WHERE id = :user_id AND status = "active"');
            $this->db->bind(':user_id', $judgeId);
            $user = $this->db->single();

            if (!$user) {
                throw new Exception('User not found or inactive');
            }

            // Step 2: Add judge to judges table (automatic role assignment)
            $this->db->query('SELECT id FROM judges WHERE show_id = :show_id AND user_id = :user_id');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $judgeId);
            $existingJudge = $this->db->single();

            // Determine if this should be active immediately or pending approval
            $isActive = ($assignmentType === 'direct') ? 1 : 0;

            if ($existingJudge) {
                // Update existing judge with appropriate status
                $this->db->query('UPDATE judges SET is_active = :is_active WHERE id = :id');
                $this->db->bind(':is_active', $isActive);
                $this->db->bind(':id', $existingJudge->id);
                $this->db->execute();
                $judgeDbId = $existingJudge->id;
            } else {
                // Add new judge with appropriate status
                $this->db->query('INSERT INTO judges (show_id, user_id, is_active) VALUES (:show_id, :user_id, :is_active)');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':user_id', $judgeId);
                $this->db->bind(':is_active', $isActive);
                $this->db->execute();
                $judgeDbId = $this->db->lastInsertId();
            }

            // Step 2: Handle category assignments
            if (empty($categoryIds)) {
                // "All Categories" selected - create individual rows for each category
                $categories = $this->showModel->getShowCategories($showId);
                foreach ($categories as $category) {
                    // Add to judge_categories table
                    $this->db->query('INSERT IGNORE INTO judge_categories (judge_id, category_id) VALUES (:judge_id, :category_id)');
                    $this->db->bind(':judge_id', $judgeDbId);
                    $this->db->bind(':category_id', $category->id);
                    $this->db->execute();
                }
            } else {
                // Specific categories selected
                foreach ($categoryIds as $categoryId) {
                    $categoryId = (int)$categoryId;
                    if ($categoryId > 0) {
                        // Add to judge_categories table
                        $this->db->query('INSERT IGNORE INTO judge_categories (judge_id, category_id) VALUES (:judge_id, :category_id)');
                        $this->db->bind(':judge_id', $judgeDbId);
                        $this->db->bind(':category_id', $categoryId);
                        $this->db->execute();
                    }
                }
            }

            // Step 3: Handle role request if assignment type is "request"
            if ($assignmentType === 'request') {
                // Create a role request for user approval
                $this->db->query('INSERT INTO show_role_requests (show_id, user_id, requested_role, requested_by, status, created_at)
                                  VALUES (:show_id, :user_id, "judge", :requested_by, "pending", NOW())
                                  ON DUPLICATE KEY UPDATE status = "pending", created_at = NOW()');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':user_id', $judgeId);
                $this->db->bind(':requested_by', $this->auth->getCurrentUserId());
                $this->db->execute();
            }

            $this->db->commit();

            // Send notifications and set appropriate success message based on assignment type
            if ($assignmentType === 'request') {
                // Send notification for request
                $this->sendJudgeRequestNotification($showId, $judgeId, $user->name);
                $this->setFlashMessage('success', 'Judge request sent to ' . $user->name . ' for approval', 'success');
            } else {
                // Send notification for direct assignment
                $this->sendJudgeApprovalNotifications($showId, $judgeId, $user->name, 'approved');
                $this->setFlashMessage('success', $user->name . ' has been assigned as a judge with selected categories', 'success');
            }

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("JudgeManagementController::assignJudge - Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to assign judge', 'danger');
        }

        $this->redirect('judge_management/index/' . $showId);
    }

    /**
     * Update judge category assignments
     */
    public function updateCategories() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('home/error');
            return;
        }

        $showId = (int)($_POST['show_id'] ?? 0);
        $judgeId = (int)($_POST['judge_id'] ?? 0);
        $categoryIds = $_POST['category_ids'] ?? [];

        if ($showId <= 0 || $judgeId <= 0) {
            $this->setFlashMessage('error', 'Invalid input data', 'danger');
            $this->redirect('judge_management/index/' . $showId);
            return;
        }

        try {
            // Get the judge's database ID
            $this->db->query('SELECT id FROM judges WHERE show_id = :show_id AND user_id = :user_id');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $judgeId);
            $judge = $this->db->single();

            if (!$judge) {
                $this->setFlashMessage('error', 'Judge not found', 'danger');
                $this->redirect('judge_management/index/' . $showId);
                return;
            }

            // Remove existing category assignments for this judge only
            $this->db->query('DELETE FROM judge_categories WHERE judge_id = :judge_id');
            $this->db->bind(':judge_id', $judge->id);
            $this->db->execute();

            // Add new category assignments
            if (empty($categoryIds)) {
                // "All Categories" selected - create individual rows for each category
                $categories = $this->showModel->getShowCategories($showId);
                foreach ($categories as $category) {
                    $this->db->query('INSERT INTO judge_categories (judge_id, category_id) VALUES (:judge_id, :category_id)');
                    $this->db->bind(':judge_id', $judge->id);
                    $this->db->bind(':category_id', $category->id);
                    $this->db->execute();
                }
            } else {
                // Specific categories selected
                foreach ($categoryIds as $categoryId) {
                    $categoryId = (int)$categoryId;
                    if ($categoryId > 0) {
                        $this->db->query('INSERT INTO judge_categories (judge_id, category_id) VALUES (:judge_id, :category_id)');
                        $this->db->bind(':judge_id', $judge->id);
                        $this->db->bind(':category_id', $categoryId);
                        $this->db->execute();
                    }
                }
            }

            $this->setFlashMessage('success', 'Judge categories updated successfully', 'success');

        } catch (Exception $e) {
            error_log("JudgeManagementController::updateCategories - Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to update judge categories', 'danger');
        }

        $this->redirect('judge_management/index/' . $showId);
    }

    /**
     * Remove judge assignment
     */
    public function removeJudge($showId, $judgeId) {
        error_log("JudgeManagementController::removeJudge - Called with showId: $showId, judgeId: $judgeId");

        $showId = (int)$showId;
        $judgeId = (int)$judgeId;

        if ($showId <= 0 || $judgeId <= 0) {
            error_log("JudgeManagementController::removeJudge - Invalid input data");
            $this->redirect('home/error');
            return;
        }

        // Check permissions
        $show = $this->showModel->getShowById($showId);
        $isAdmin = $this->auth->hasRole('admin');
        $isCoordinator = $this->auth->hasRole('coordinator') && $show->coordinator_id == $this->auth->getCurrentUserId();

        if (!$isAdmin && !$isCoordinator) {
            $this->redirect('home/access_denied');
            return;
        }

        try {
            // Get the judge's database ID and user info
            $this->db->query('SELECT j.id, u.name FROM judges j JOIN users u ON j.user_id = u.id WHERE j.show_id = :show_id AND j.user_id = :user_id');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $judgeId);
            $judge = $this->db->single();

            if ($judge) {
                error_log("JudgeManagementController::removeJudge - Found judge: " . $judge->name . ", starting removal");
                $this->db->beginTransaction();

                // 1. Remove category assignments for this judge
                $this->db->query('DELETE FROM judge_categories WHERE judge_id = :judge_id');
                $this->db->bind(':judge_id', $judge->id);
                $this->db->execute();

                // 2. Remove from judges table completely
                $this->db->query('DELETE FROM judges WHERE id = :judge_id');
                $this->db->bind(':judge_id', $judge->id);
                $this->db->execute();

                // 3. Remove show role assignment completely (if exists)
                $this->db->query('DELETE FROM show_role_assignments
                                  WHERE show_id = :show_id AND user_id = :user_id AND assigned_role = "judge"');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':user_id', $judgeId);
                $this->db->execute();

                // 4. Remove any pending role requests completely (if exists)
                $this->db->query('DELETE FROM show_role_requests
                                  WHERE show_id = :show_id AND user_id = :user_id AND requested_role = "judge"');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':user_id', $judgeId);
                $this->db->execute();

                $this->db->commit();
                error_log("JudgeManagementController::removeJudge - Successfully removed judge: " . $judge->name);
                $this->setFlashMessage('success', $judge->name . ' has been completely removed as a judge (role and categories)', 'success');
            } else {
                error_log("JudgeManagementController::removeJudge - Judge not found for showId: $showId, judgeId: $judgeId");
                $this->setFlashMessage('error', 'Judge not found', 'danger');
            }

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("JudgeManagementController::removeJudge - Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to remove judge', 'danger');
        }

        $this->redirect('judge_management/index/' . $showId);
    }

    /**
     * Get judge details for editing (AJAX)
     */
    public function getJudgeDetails($showId, $judgeId) {
        // Only allow AJAX requests
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request']);
            return;
        }

        try {
            // Get judge's current category assignments using existing system
            $this->db->query('
                SELECT jc.category_id, sc.name as category_name
                FROM judges j
                JOIN judge_categories jc ON j.id = jc.judge_id
                LEFT JOIN show_categories sc ON jc.category_id = sc.id
                WHERE j.show_id = :show_id AND j.user_id = :judge_id
            ');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':judge_id', $judgeId);
            $assignments = $this->db->resultSet();

            // Get judge info
            $this->db->query('SELECT name, email, phone FROM users WHERE id = :judge_id');
            $this->db->bind(':judge_id', $judgeId);
            $judge = $this->db->single();

            echo json_encode([
                'success' => true,
                'judge' => $judge,
                'assignments' => $assignments
            ]);

        } catch (Exception $e) {
            error_log("JudgeManagementController::getJudgeDetails - Error: " . $e->getMessage());
            echo json_encode(['error' => 'Failed to get judge details']);
        }
    }

    /**
     * Send message to judge
     */
    public function sendMessage() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('home/error');
            return;
        }

        $showId = (int)($_POST['show_id'] ?? 0);
        $judgeId = (int)($_POST['judge_id'] ?? 0);
        $message = trim($_POST['message'] ?? '');
        $subject = trim($_POST['subject'] ?? '');

        if ($showId <= 0 || $judgeId <= 0 || empty($message)) {
            $this->setFlashMessage('error', 'Please fill in all required fields', 'danger');
            $this->redirect('judge_management/index/' . $showId);
            return;
        }

        // Check permissions
        $show = $this->showModel->getShowById($showId);
        $isAdmin = $this->auth->hasRole('admin');
        $isCoordinator = $this->auth->hasRole('coordinator') && $show->coordinator_id == $this->auth->getCurrentUserId();

        if (!$isAdmin && !$isCoordinator) {
            $this->redirect('home/access_denied');
            return;
        }

        try {
            // Get judge details
            $this->db->query('SELECT name, email FROM users WHERE id = :judge_id');
            $this->db->bind(':judge_id', $judgeId);
            $judge = $this->db->single();

            // Get sender details
            $senderId = $this->auth->getCurrentUserId();
            $this->db->query('SELECT name, email FROM users WHERE id = :sender_id');
            $this->db->bind(':sender_id', $senderId);
            $sender = $this->db->single();

            if (!$judge || !$sender) {
                throw new Exception('Judge or sender not found');
            }

            // Prepare email
            $emailSubject = $subject ?: "Message from {$sender->name} regarding {$show->name}";
            $emailMessage = "Hello {$judge->name},\n\n";
            $emailMessage .= "You have received a message from {$sender->name} regarding the show '{$show->name}':\n\n";
            $emailMessage .= "---\n";
            $emailMessage .= $message . "\n";
            $emailMessage .= "---\n\n";
            $emailMessage .= "You can reply to this message by contacting {$sender->name} at {$sender->email}.\n\n";
            $emailMessage .= "Thank you!";

            // Send email (using your existing notification system)
            $notificationData = [
                'type' => 'judge_message',
                'show_id' => $showId,
                'sender_id' => $senderId,
                'subject' => $emailSubject
            ];

            $result = $this->showRoleModel->queueNotification($judgeId, 'email', $emailSubject, $emailMessage, $notificationData);

            if ($result) {
                $this->setFlashMessage('success', 'Message sent successfully to ' . $judge->name, 'success');
            } else {
                $this->setFlashMessage('error', 'Failed to send message', 'danger');
            }

        } catch (Exception $e) {
            error_log("JudgeManagementController::sendMessage - Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to send message', 'danger');
        }

        $this->redirect('judge_management/index/' . $showId);
    }

    /**
     * Admin override to approve a pending judge request
     */
    public function approveJudgeRequest($showId, $judgeId) {
        error_log("JudgeManagementController::approveJudgeRequest - Called with showId: $showId, judgeId: $judgeId");

        // Only allow admins to override approvals
        if (!$this->auth->hasRole('admin')) {
            error_log("JudgeManagementController::approveJudgeRequest - Access denied, not admin");
            $this->redirect('home/access_denied');
            return;
        }

        $showId = (int)$showId;
        $judgeId = (int)$judgeId;

        if ($showId <= 0 || $judgeId <= 0) {
            error_log("JudgeManagementController::approveJudgeRequest - Invalid input data");
            $this->setFlashMessage('error', 'Invalid input data', 'danger');
            $this->redirect('judge_management/index/' . $showId);
            return;
        }

        try {
            // Get the judge request (pending or denied)
            $this->db->query('SELECT j.id, u.name, j.is_active FROM judges j JOIN users u ON j.user_id = u.id
                              WHERE j.show_id = :show_id AND j.user_id = :user_id AND j.is_active IN (0, -1)');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $judgeId);
            $pendingJudge = $this->db->single();

            if (!$pendingJudge) {
                error_log("JudgeManagementController::approveJudgeRequest - No pending judge found for showId: $showId, judgeId: $judgeId");
                $this->setFlashMessage('error', 'No pending or denied judge request found', 'danger');
                $this->redirect('judge_management/index/' . $showId);
                return;
            }

            error_log("JudgeManagementController::approveJudgeRequest - Found judge: " . $pendingJudge->name . ", current status: " . $pendingJudge->is_active);

            $this->db->beginTransaction();

            // 1. Activate the judge (approve the request)
            $this->db->query('UPDATE judges SET is_active = 1 WHERE id = :judge_id');
            $this->db->bind(':judge_id', $pendingJudge->id);
            $this->db->execute();

            // 2. Update any pending role requests to approved
            $this->db->query('UPDATE show_role_requests SET status = "approved", responded_at = NOW()
                              WHERE show_id = :show_id AND user_id = :user_id AND requested_role = "judge" AND status = "pending"');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $judgeId);
            $this->db->execute();

            // 3. Add to show_role_assignments if not already there
            $this->db->query('INSERT IGNORE INTO show_role_assignments (show_id, user_id, assigned_role, assigned_by, is_active)
                              VALUES (:show_id, :user_id, "judge", :admin_id, 1)');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $judgeId);
            $this->db->bind(':admin_id', $this->auth->getCurrentUserId());
            $this->db->execute();

            $this->db->commit();

            // Send notifications to user and coordinator
            $this->sendJudgeApprovalNotifications($showId, $judgeId, $pendingJudge->name, 'approved');

            $this->setFlashMessage('success', 'Admin Override: ' . $pendingJudge->name . ' has been approved as a judge', 'success');

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("JudgeManagementController::approveJudgeRequest - Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to approve judge request', 'danger');
        }

        $this->redirect('judge_management/index/' . $showId);
    }

    /**
     * Admin deny a pending judge request with note
     */
    public function denyJudgeRequest($showId, $judgeId) {
        // Only allow admins to deny requests
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('judge_management/index/' . $showId);
            return;
        }

        $showId = (int)$showId;
        $judgeId = (int)$judgeId;
        $denialNote = trim($_POST['denial_note'] ?? '');

        if ($showId <= 0 || $judgeId <= 0 || empty($denialNote)) {
            $this->setFlashMessage('error', 'Invalid input data or missing denial note', 'danger');
            $this->redirect('judge_management/index/' . $showId);
            return;
        }

        try {
            // Get the pending judge request
            $this->db->query('SELECT j.id, u.name FROM judges j JOIN users u ON j.user_id = u.id
                              WHERE j.show_id = :show_id AND j.user_id = :user_id');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $judgeId);
            $pendingJudge = $this->db->single();

            if (!$pendingJudge) {
                $this->setFlashMessage('error', 'Judge request not found', 'danger');
                $this->redirect('judge_management/index/' . $showId);
                return;
            }

            $this->db->beginTransaction();

            // 1. Update judge status to denied with note
            $this->db->query('UPDATE judges SET is_active = -1, denial_note = :denial_note, denied_by = :admin_id, denied_at = NOW()
                              WHERE id = :judge_id');
            $this->db->bind(':denial_note', $denialNote);
            $this->db->bind(':admin_id', $this->auth->getCurrentUserId());
            $this->db->bind(':judge_id', $pendingJudge->id);
            $this->db->execute();

            // 2. Update role requests to declined (show_role_requests uses 'declined' not 'denied')
            $this->db->query('UPDATE show_role_requests SET status = "declined", response_message = :denial_note, responded_at = NOW()
                              WHERE show_id = :show_id AND user_id = :user_id AND requested_role = "judge" AND status = "pending"');
            $this->db->bind(':denial_note', $denialNote);
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $judgeId);
            $this->db->execute();

            $this->db->commit();

            // Send notifications to user and coordinator
            $this->sendJudgeApprovalNotifications($showId, $judgeId, $pendingJudge->name, 'denied');

            $this->setFlashMessage('success', $pendingJudge->name . ' judge request has been denied with note', 'success');

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("JudgeManagementController::denyJudgeRequest - Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to deny judge request', 'danger');
        }

        $this->redirect('judge_management/index/' . $showId);
    }

    /**
     * AJAX search for users (Admin only)
     */
    public function searchUsers() {
        // Only allow AJAX requests
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request']);
            return;
        }

        // Only allow admins
        if (!$this->auth->hasRole('admin')) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }

        // Get search query
        $input = json_decode(file_get_contents('php://input'), true);
        $query = trim($input['query'] ?? '');

        if (strlen($query) < 2) {
            echo json_encode([]);
            return;
        }

        try {
            // Search users by name, email, or phone
            $this->db->query('
                SELECT id, name, email, phone, role
                FROM users
                WHERE status = "active"
                    AND (
                        name LIKE :query1 OR
                        email LIKE :query2 OR
                        phone LIKE :query3
                    )
                    AND role IN ("judge", "admin", "coordinator")
                ORDER BY
                    CASE
                        WHEN name LIKE :query4 THEN 1
                        WHEN email LIKE :query5 THEN 2
                        ELSE 3
                    END,
                    name
                LIMIT 10
            ');

            $searchTerm = '%' . $query . '%';
            $this->db->bind(':query1', $searchTerm);
            $this->db->bind(':query2', $searchTerm);
            $this->db->bind(':query3', $searchTerm);
            $this->db->bind(':query4', $query . '%'); // Exact start match for name
            $this->db->bind(':query5', $query . '%'); // Exact start match for email

            $users = $this->db->resultSet();

            echo json_encode($users);

        } catch (Exception $e) {
            error_log("JudgeManagementController::searchUsers - Error: " . $e->getMessage());
            echo json_encode(['error' => 'Search failed']);
        }
    }

    /**
     * Send notifications for judge approval/denial
     */
    private function sendJudgeApprovalNotifications($showId, $judgeId, $judgeName, $status) {
        try {
            // Get show and user details
            $show = $this->showModel->getShowById($showId);
            $user = $this->userModel->getUserById($judgeId);
            $coordinator = $this->userModel->getUserById($show->coordinator_id);

            if (!$show || !$user) {
                return;
            }

            // Load notification service
            require_once APPROOT . '/models/NotificationService.php';
            $notificationService = new NotificationService();

            // Prepare notification content based on status
            if ($status === 'approved') {
                $userSubject = "Judge Request Approved - {$show->name}";
                $userMessage = "Your request to be a judge for '{$show->name}' has been approved by an administrator. You can now access the judging interface for this show.";

                $coordinatorSubject = "Judge Request Approved - {$show->name}";
                $coordinatorMessage = "The judge request for {$judgeName} has been approved by an administrator for your show '{$show->name}'.";
            } else {
                $userSubject = "Judge Request Update - {$show->name}";
                $userMessage = "There has been an update to your judge request for '{$show->name}'. Please check the system for details.";

                $coordinatorSubject = "Judge Request Update - {$show->name}";
                $coordinatorMessage = "There has been an update to the judge request for {$judgeName} for your show '{$show->name}'.";
            }

            // Send notification to the user
            if (!empty($user->email)) {
                $notificationService->sendTestNotification(
                    $user->id,
                    'email',
                    $userSubject,
                    $userMessage
                );
            }

            // Send notification to coordinator if different from user
            if ($coordinator && $coordinator->id !== $user->id && !empty($coordinator->email)) {
                $notificationService->sendTestNotification(
                    $coordinator->id,
                    'email',
                    $coordinatorSubject,
                    $coordinatorMessage
                );
            }

        } catch (Exception $e) {
            error_log("JudgeManagementController::sendJudgeApprovalNotifications - Error: " . $e->getMessage());
            // Don't throw exception - notifications are not critical for the main operation
        }
    }

    /**
     * Send notification for judge request
     */
    private function sendJudgeRequestNotification($showId, $judgeId, $judgeName) {
        try {
            // Get show and user details
            $show = $this->showModel->getShowById($showId);
            $user = $this->userModel->getUserById($judgeId);
            $coordinator = $this->userModel->getUserById($show->coordinator_id);

            if (!$show || !$user) {
                return;
            }

            // Load notification service
            require_once APPROOT . '/models/NotificationService.php';
            $notificationService = new NotificationService();

            // Prepare notification content
            $userSubject = "Judge Request - {$show->name}";
            $userMessage = "You have been requested to be a judge for '{$show->name}' scheduled for " . date('F j, Y', strtotime($show->event_date)) . ". Please log in to the system to accept or decline this request.";

            $coordinatorSubject = "Judge Request Sent - {$show->name}";
            $coordinatorMessage = "A judge request has been sent to {$judgeName} for your show '{$show->name}'. They will receive a notification to accept or decline the request.";

            // Send notification to the user
            if (!empty($user->email)) {
                $notificationService->sendTestNotification(
                    $user->id,
                    'email',
                    $userSubject,
                    $userMessage
                );
            }

            // Send notification to coordinator if different from user
            if ($coordinator && $coordinator->id !== $user->id && !empty($coordinator->email)) {
                $notificationService->sendTestNotification(
                    $coordinator->id,
                    'email',
                    $coordinatorSubject,
                    $coordinatorMessage
                );
            }

        } catch (Exception $e) {
            error_log("JudgeManagementController::sendJudgeRequestNotification - Error: " . $e->getMessage());
            // Don't throw exception - notifications are not critical for the main operation
        }
    }
}
