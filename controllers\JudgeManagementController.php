<?php

/**
 * Unified Judge Management Controller
 * 
 * Provides a single, comprehensive interface for managing judges
 * for both coordinators and administrators
 */
class JudgeManagementController extends Controller {
    private $showModel;
    private $showRoleModel;
    private $userModel;
    private $auth;
    private $db;

    public function __construct() {
        parent::__construct();
        
        $this->auth = new Auth();
        $this->db = new Database();
        $this->showModel = new ShowModel();
        $this->showRoleModel = new ShowRoleModel();
        $this->userModel = new UserModel();

        // Require login
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
    }

    /**
     * Unified judge management dashboard
     */
    public function index($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }

        // Check permissions
        $isAdmin = $this->auth->hasRole('admin');
        $isCoordinator = $this->auth->hasRole('coordinator') && $show->coordinator_id == $this->auth->getCurrentUserId();
        
        if (!$isAdmin && !$isCoordinator) {
            $this->redirect('home/access_denied');
            return;
        }

        // Get comprehensive judge data
        $judgeData = $this->getComprehensiveJudgeData($showId);
        
        $data = [
            'title' => 'Judge Management - ' . $show->name,
            'show' => $show,
            'judge_data' => $judgeData,
            'categories' => $this->showModel->getShowCategories($showId),
            'available_judges' => $this->getAvailableJudges($showId),
            'is_admin' => $isAdmin,
            'is_coordinator' => $isCoordinator,
            'judging_progress' => $this->getJudgingProgress($showId)
        ];

        $this->view('judge_management/dashboard', $data);
    }

    /**
     * Get comprehensive judge data combining role assignments and category assignments
     */
    private function getComprehensiveJudgeData($showId) {
        try {
            // Get judges with role assignments and category assignments
            $this->db->query('
                SELECT DISTINCT
                    u.id as user_id,
                    u.name as judge_name,
                    u.email as judge_email,
                    sra.assigned_role,
                    sra.is_active as role_active,
                    sra.assigned_at as role_assigned_at,
                    GROUP_CONCAT(DISTINCT sc.name ORDER BY sc.name) as assigned_categories,
                    GROUP_CONCAT(DISTINCT ja.category_id ORDER BY ja.category_id) as category_ids,
                    COUNT(DISTINCT ja.id) as category_count,
                    CASE 
                        WHEN sra.id IS NOT NULL AND sra.is_active = 1 THEN "approved"
                        WHEN srr.id IS NOT NULL AND srr.status = "pending" THEN "pending"
                        ELSE "not_assigned"
                    END as status
                FROM users u
                LEFT JOIN show_role_assignments sra ON u.id = sra.user_id 
                    AND sra.show_id = :show_id 
                    AND sra.assigned_role = "judge" 
                    AND sra.is_active = 1
                LEFT JOIN show_role_requests srr ON u.id = srr.user_id 
                    AND srr.show_id = :show_id2 
                    AND srr.requested_role = "judge" 
                    AND srr.status = "pending"
                LEFT JOIN judge_assignments ja ON u.id = ja.judge_id AND ja.show_id = :show_id3
                LEFT JOIN show_categories sc ON ja.category_id = sc.id
                WHERE (sra.id IS NOT NULL OR srr.id IS NOT NULL OR ja.id IS NOT NULL)
                    AND u.role IN ("judge", "admin", "coordinator")
                GROUP BY u.id, u.name, u.email, sra.assigned_role, sra.is_active, sra.assigned_at
                ORDER BY u.name
            ');
            
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':show_id2', $showId);
            $this->db->bind(':show_id3', $showId);
            
            return $this->db->resultSet();
            
        } catch (Exception $e) {
            error_log("JudgeManagementController::getComprehensiveJudgeData - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get available judges who can be assigned
     */
    private function getAvailableJudges($showId) {
        try {
            $this->db->query('
                SELECT u.id, u.name, u.email, u.role
                FROM users u
                WHERE u.role IN ("judge", "admin", "coordinator")
                    AND u.status = "active"
                    AND u.id NOT IN (
                        SELECT sra.user_id 
                        FROM show_role_assignments sra 
                        WHERE sra.show_id = :show_id 
                            AND sra.assigned_role = "judge" 
                            AND sra.is_active = 1
                    )
                ORDER BY u.name
            ');
            
            $this->db->bind(':show_id', $showId);
            return $this->db->resultSet();
            
        } catch (Exception $e) {
            error_log("JudgeManagementController::getAvailableJudges - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get judging progress statistics
     */
    private function getJudgingProgress($showId) {
        try {
            // Get total vehicles to judge
            $this->db->query('
                SELECT COUNT(*) as total_vehicles
                FROM registrations r
                WHERE r.show_id = :show_id AND r.status = "approved"
            ');
            $this->db->bind(':show_id', $showId);
            $totalVehicles = $this->db->single()->total_vehicles ?? 0;

            // Get judging completion by category
            $this->db->query('
                SELECT 
                    sc.id as category_id,
                    sc.name as category_name,
                    COUNT(DISTINCT r.id) as vehicles_in_category,
                    COUNT(DISTINCT CASE WHEN s.id IS NOT NULL THEN r.id END) as vehicles_judged
                FROM show_categories sc
                LEFT JOIN registrations r ON sc.id = r.category_id AND r.show_id = :show_id AND r.status = "approved"
                LEFT JOIN scores s ON r.id = s.registration_id AND s.is_draft = 0
                WHERE sc.show_id = :show_id2
                GROUP BY sc.id, sc.name
                ORDER BY sc.name
            ');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':show_id2', $showId);
            $categoryProgress = $this->db->resultSet();

            return [
                'total_vehicles' => $totalVehicles,
                'category_progress' => $categoryProgress
            ];
            
        } catch (Exception $e) {
            error_log("JudgeManagementController::getJudgingProgress - Error: " . $e->getMessage());
            return ['total_vehicles' => 0, 'category_progress' => []];
        }
    }

    /**
     * Assign judge role and categories in one action
     */
    public function assignJudge() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('home/error');
            return;
        }

        $showId = (int)($_POST['show_id'] ?? 0);
        $judgeId = (int)($_POST['judge_id'] ?? 0);
        $categoryIds = $_POST['category_ids'] ?? [];
        $assignmentType = $_POST['assignment_type'] ?? 'direct'; // direct or request

        if ($showId <= 0 || $judgeId <= 0) {
            $this->setFlashMessage('error', 'Invalid input data', 'danger');
            $this->redirect('judge_management/index/' . $showId);
            return;
        }

        // Check permissions
        $show = $this->showModel->getShowById($showId);
        $isAdmin = $this->auth->hasRole('admin');
        $isCoordinator = $this->auth->hasRole('coordinator') && $show->coordinator_id == $this->auth->getCurrentUserId();
        
        if (!$isAdmin && !$isCoordinator) {
            $this->redirect('home/access_denied');
            return;
        }

        try {
            $this->db->beginTransaction();

            // Step 1: Assign judge role
            if ($isAdmin || $assignmentType === 'direct') {
                // Direct assignment
                $roleResult = $this->showRoleModel->createDirectAssignment(
                    $showId, 
                    $judgeId, 
                    'judge', 
                    $this->auth->getCurrentUserId()
                );
            } else {
                // Request assignment (coordinator)
                $roleResult = $this->showRoleModel->createRoleRequest(
                    $showId, 
                    $judgeId, 
                    'judge', 
                    $this->auth->getCurrentUserId(),
                    'Judge assignment for ' . $show->name
                );
            }

            if (!$roleResult) {
                throw new Exception('Failed to assign judge role');
            }

            // Step 2: Assign categories (only if direct assignment)
            if ($isAdmin || $assignmentType === 'direct') {
                foreach ($categoryIds as $categoryId) {
                    $categoryId = (int)$categoryId;
                    if ($categoryId > 0) {
                        $this->showModel->assignJudge([
                            'show_id' => $showId,
                            'judge_id' => $judgeId,
                            'category_id' => $categoryId
                        ]);
                    }
                }

                // If no categories selected, assign to all categories
                if (empty($categoryIds)) {
                    $this->showModel->assignJudge([
                        'show_id' => $showId,
                        'judge_id' => $judgeId,
                        'category_id' => null // All categories
                    ]);
                }
            }

            $this->db->commit();

            $message = $assignmentType === 'direct' ? 'Judge assigned successfully' : 'Judge assignment request sent';
            $this->setFlashMessage('success', $message, 'success');

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("JudgeManagementController::assignJudge - Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to assign judge', 'danger');
        }

        $this->redirect('judge_management/index/' . $showId);
    }

    /**
     * Update judge category assignments
     */
    public function updateCategories() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('home/error');
            return;
        }

        $showId = (int)($_POST['show_id'] ?? 0);
        $judgeId = (int)($_POST['judge_id'] ?? 0);
        $categoryIds = $_POST['category_ids'] ?? [];

        // Implementation continues...
        $this->redirect('judge_management/index/' . $showId);
    }

    /**
     * Remove judge assignment
     */
    public function removeJudge($showId, $judgeId) {
        // Implementation for removing judge and all category assignments
        $this->redirect('judge_management/index/' . $showId);
    }
}
