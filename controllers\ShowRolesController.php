<?php

/**
 * ShowRolesController
 *
 * Handles per-show role assignments with approval workflow
 *
 * @version 1.0
 * @date 2025-07-10
 */
class ShowRolesController extends Controller {
    private $showRoleModel;
    private $showModel;
    private $userModel;
    private $auth;
    private $db;

    public function __construct() {
        parent::__construct();

        // Initialize auth and database
        $this->auth = new Auth();
        $this->db = new Database();

        // Require login for all methods
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }

        // Initialize models
        $this->showRoleModel = $this->model('ShowRoleModel');
        $this->showModel = $this->model('ShowModel');
        $this->userModel = $this->model('UserModel');
    }

    /**
     * Default index method - redirects based on user role
     */
    public function index() {
        if ($this->auth->hasRole('admin')) {
            $this->adminOverview();
        } else {
            $this->myRequests();
        }
    }

    /**
     * Manage role assignments for a show (Admin/Coordinator access)
     *
     * @param int $showId Show ID
     */
    public function manage($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check permissions
        $userId = $this->auth->getCurrentUserId();
        $isAdmin = $this->auth->hasRole('admin');
        $isCoordinator = $this->auth->hasRole('coordinator') && $show->coordinator_id == $userId;
        
        if (!$isAdmin && !$isCoordinator) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get current assignments
        $assignments = $this->showRoleModel->getShowAssignments($showId);
        
        // Get pending requests (for coordinators)
        $pendingRequests = [];
        if (!$isAdmin) {
            $this->db->query('SELECT srr.*, u.name as user_name, u.email as user_email
                              FROM show_role_requests srr
                              JOIN users u ON srr.user_id = u.id
                              WHERE srr.show_id = :show_id AND srr.status = "pending" 
                              AND srr.requested_by = :user_id AND srr.expires_at > NOW()
                              ORDER BY srr.requested_at DESC');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $userId);
            $pendingRequests = $this->db->resultSet();
        }
        
        $data = [
            'title' => 'Manage Show Roles - ' . $show->name,
            'show' => $show,
            'assignments' => $assignments,
            'pending_requests' => $pendingRequests,
            'is_admin' => $isAdmin,
            'is_coordinator' => $isCoordinator
        ];
        
        $this->view('show_roles/manage', $data);
    }
    
    /**
     * Search users for assignment (AJAX)
     */
    public function searchUsers() {
        // Only allow AJAX requests
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request']);
            return;
        }
        
        // Check if user is admin or coordinator
        if (!$this->auth->hasRole(['admin', 'coordinator'])) {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }
        
        $searchTerm = $_GET['q'] ?? '';
        
        if (strlen($searchTerm) < 2) {
            echo json_encode(['users' => []]);
            return;
        }
        
        $users = $this->showRoleModel->searchUsers($searchTerm, 10);
        
        // Format for select2
        $formattedUsers = [];
        foreach ($users as $user) {
            $formattedUsers[] = [
                'id' => $user->id,
                'text' => $user->name . ' (' . $user->email . ') - ID: ' . $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role
            ];
        }
        
        echo json_encode(['users' => $formattedUsers]);
    }
    
    /**
     * Assign role to user
     */
    public function assign() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('home/error');
            return;
        }
        
        $showId = (int)($_POST['show_id'] ?? 0);
        $userId = (int)($_POST['user_id'] ?? 0);
        $role = $_POST['role'] ?? '';
        $message = $_POST['message'] ?? '';
        
        // Validate inputs
        if ($showId <= 0 || $userId <= 0 || empty($role)) {
            $this->setFlashMessage('error', 'Invalid input data', 'danger');
            $this->redirect('show_roles/manage/' . $showId);
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check permissions
        $currentUserId = $this->auth->getCurrentUserId();
        $isAdmin = $this->auth->hasRole('admin');
        $isCoordinator = $this->auth->hasRole('coordinator') && $show->coordinator_id == $currentUserId;
        
        if (!$isAdmin && !$isCoordinator) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Admin can assign directly, coordinator needs approval
        $isAdminAssignment = $isAdmin;
        
        $result = $this->showRoleModel->requestRoleAssignment(
            $showId, 
            $userId, 
            $role, 
            $currentUserId, 
            $message, 
            $isAdminAssignment
        );
        
        if ($result) {
            if ($isAdminAssignment) {
                $this->setFlashMessage('success', 'Role assigned successfully', 'success');
            } else {
                $this->setFlashMessage('success', 'Role assignment request sent to user for approval', 'success');
            }
        } else {
            $this->setFlashMessage('error', 'Failed to assign role. User may already have this role or a pending request exists.', 'danger');
        }
        
        $this->redirect('show_roles/manage/' . $showId);
    }
    
    /**
     * Remove role assignment
     */
    public function remove() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('home/error');
            return;
        }
        
        $assignmentId = (int)($_POST['assignment_id'] ?? 0);
        $showId = (int)($_POST['show_id'] ?? 0);
        
        if ($assignmentId <= 0 || $showId <= 0) {
            $this->setFlashMessage('error', 'Invalid assignment ID', 'danger');
            $this->redirect('show_roles/manage/' . $showId);
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check permissions
        $currentUserId = $this->auth->getCurrentUserId();
        $isAdmin = $this->auth->hasRole('admin');
        $isCoordinator = $this->auth->hasRole('coordinator') && $show->coordinator_id == $currentUserId;
        
        if (!$isAdmin && !$isCoordinator) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $result = $this->showRoleModel->removeAssignment($assignmentId, $currentUserId);
        
        if ($result) {
            $this->setFlashMessage('success', 'Role assignment removed successfully', 'success');
        } else {
            $this->setFlashMessage('error', 'Failed to remove role assignment', 'danger');
        }
        
        $this->redirect('show_roles/manage/' . $showId);
    }
    
    /**
     * User's pending role requests
     */
    public function myRequests() {
        $userId = $this->auth->getCurrentUserId();

        $pendingRequests = $this->showRoleModel->getUserPendingRequests($userId);
        $assignments = $this->showRoleModel->getUserAssignments($userId);

        $data = [
            'title' => 'My Show Role Assignments',
            'pending_requests' => $pendingRequests,
            'assignments' => $assignments
        ];

        $this->view('show_roles/my_requests', $data);
    }
    
    /**
     * Admin overview of all role assignments
     */
    public function adminOverview() {
        // Check admin access
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get all active assignments
        $this->db->query('SELECT sra.*, s.name as show_name, s.start_date, s.end_date,
                          u.name as user_name, u.email as user_email,
                          assigner.name as assigned_by_name
                          FROM show_role_assignments sra
                          JOIN shows s ON sra.show_id = s.id
                          JOIN users u ON sra.user_id = u.id
                          JOIN users assigner ON sra.assigned_by = assigner.id
                          WHERE sra.is_active = 1
                          ORDER BY s.start_date DESC, sra.assigned_role, u.name');
        $allAssignments = $this->db->resultSet();
        
        // Get all pending requests
        $this->db->query('SELECT srr.*, s.name as show_name, s.start_date, s.end_date,
                          u.name as user_name, u.email as user_email,
                          requester.name as requested_by_name
                          FROM show_role_requests srr
                          JOIN shows s ON srr.show_id = s.id
                          JOIN users u ON srr.user_id = u.id
                          JOIN users requester ON srr.requested_by = requester.id
                          WHERE srr.status = "pending" AND srr.expires_at > NOW()
                          ORDER BY srr.requested_at DESC');
        $allPendingRequests = $this->db->resultSet();
        
        // Get statistics
        $stats = [
            'total_assignments' => count($allAssignments),
            'total_pending' => count($allPendingRequests),
            'coordinator_assignments' => count(array_filter($allAssignments, fn($a) => $a->assigned_role === 'coordinator')),
            'judge_assignments' => count(array_filter($allAssignments, fn($a) => $a->assigned_role === 'judge')),
            'staff_assignments' => count(array_filter($allAssignments, fn($a) => $a->assigned_role === 'staff'))
        ];
        
        $data = [
            'title' => 'Show Role Manager - Admin Overview',
            'all_assignments' => $allAssignments,
            'all_pending_requests' => $allPendingRequests,
            'stats' => $stats
        ];
        
        $this->view('show_roles/admin_overview', $data);
    }
    
    /**
     * Respond to role assignment request
     */
    public function respond() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('home/error');
            return;
        }
        
        $requestId = (int)($_POST['request_id'] ?? 0);
        $response = $_POST['response'] ?? '';
        $message = $_POST['message'] ?? '';
        
        if ($requestId <= 0 || !in_array($response, ['approved', 'declined'])) {
            $this->setFlashMessage('error', 'Invalid request data', 'danger');
            $this->redirect('show_roles/myRequests');
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $result = $this->showRoleModel->respondToRequest($requestId, $userId, $response, $message);
        
        if ($result) {
            $responseText = $response === 'approved' ? 'accepted' : 'declined';
            $this->setFlashMessage('success', "Role assignment request {$responseText} successfully", 'success');
        } else {
            $this->setFlashMessage('error', 'Failed to respond to request. It may have expired.', 'danger');
        }
        
        $this->redirect('show_roles/myRequests');
    }
}
