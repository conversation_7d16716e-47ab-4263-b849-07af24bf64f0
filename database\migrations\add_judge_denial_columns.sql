-- Add denial functionality columns to judges table
-- This migration adds support for denying judge requests with notes

-- Add denial columns to judges table
ALTER TABLE judges
ADD COLUMN denial_note TEXT NULL AFTER is_active,
ADD COLUMN denied_by INT(10) UNSIGNED NULL AFTER denial_note,
ADD COLUMN denied_at TIMESTAMP NULL AFTER denied_by;

-- Add foreign key for denied_by (references users table)
ALTER TABLE judges
ADD CONSTRAINT fk_judges_denied_by
FOR<PERSON><PERSON><PERSON> KEY (denied_by) REFERENCES users(id) ON DELETE SET NULL;

-- Add denial columns to show_role_requests table if it exists
ALTER TABLE show_role_requests
ADD COLUMN denial_note TEXT NULL AFTER status,
ADD COLUMN denied_by INT(10) UNSIGNED NULL AFTER denial_note,
ADD COLUMN denied_at TIMESTAMP NULL AFTER denied_by;

-- Add foreign key for denied_by in show_role_requests
ALTER TABLE show_role_requests
ADD CONSTRAINT fk_show_role_requests_denied_by
<PERSON>OR<PERSON><PERSON><PERSON> KEY (denied_by) REFERENCES users(id) ON DELETE SET NULL;

-- Add index for better performance on denied status queries
ALTER TABLE judges ADD INDEX idx_judges_is_active_denied (is_active, denied_at);
ALTER TABLE show_role_requests ADD INDEX idx_show_role_requests_status_denied (status, denied_at);
