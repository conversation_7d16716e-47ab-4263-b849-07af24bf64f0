[11-Jul-2025 11:36:48 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:36:48 UTC] Raw URL: notification/getUnread
[11-Jul-2025 11:36:48 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[11-Jul-2025 11:36:48 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[11-Jul-2025 11:36:48 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[11-Jul-2025 11:36:48 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[11-Jul-2025 11:36:48 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[11-Jul-2025 11:36:48 UTC] App.php: About to call method: getUnread on controller: NotificationController
[11-Jul-2025 11:36:48 UTC] Database::resultSet - Executing query: SQL: [293] SELECT * FROM notification_queue 
                             WHERE user_id = :user_id 
                             AND status = :status 
                             AND scheduled_for <= NOW()
                             ORDER BY created_at DESC 
                             LIMIT 50
Params:  2
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2
Key: Name: [7] :status
paramno=1
name=[7] ":status"
is_param=1
param_type=2

[11-Jul-2025 11:36:48 UTC] Database::resultSet - Result count: 0
[11-Jul-2025 11:36:48 UTC] Database::resultSet - No results found
[11-Jul-2025 11:36:53 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:36:53 UTC] Raw URL: notification/getUnread
[11-Jul-2025 11:36:53 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[11-Jul-2025 11:36:53 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[11-Jul-2025 11:36:53 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[11-Jul-2025 11:36:53 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[11-Jul-2025 11:36:53 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[11-Jul-2025 11:36:53 UTC] App.php: About to call method: getUnread on controller: NotificationController
[11-Jul-2025 11:36:53 UTC] Database::resultSet - Executing query: SQL: [293] SELECT * FROM notification_queue 
                             WHERE user_id = :user_id 
                             AND status = :status 
                             AND scheduled_for <= NOW()
                             ORDER BY created_at DESC 
                             LIMIT 50
Params:  2
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2
Key: Name: [7] :status
paramno=1
name=[7] ":status"
is_param=1
param_type=2

[11-Jul-2025 11:36:53 UTC] Database::resultSet - Result count: 0
[11-Jul-2025 11:36:53 UTC] Database::resultSet - No results found
[11-Jul-2025 11:36:56 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:36:56 UTC] Raw URL: notification/getUnread
[11-Jul-2025 11:36:56 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[11-Jul-2025 11:36:56 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[11-Jul-2025 11:36:56 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[11-Jul-2025 11:36:56 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[11-Jul-2025 11:36:56 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[11-Jul-2025 11:36:56 UTC] App.php: About to call method: getUnread on controller: NotificationController
[11-Jul-2025 11:36:56 UTC] Database::resultSet - Executing query: SQL: [293] SELECT * FROM notification_queue 
                             WHERE user_id = :user_id 
                             AND status = :status 
                             AND scheduled_for <= NOW()
                             ORDER BY created_at DESC 
                             LIMIT 50
Params:  2
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2
Key: Name: [7] :status
paramno=1
name=[7] ":status"
is_param=1
param_type=2

[11-Jul-2025 11:36:56 UTC] Database::resultSet - Result count: 0
[11-Jul-2025 11:36:56 UTC] Database::resultSet - No results found
[11-Jul-2025 11:36:58 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:36:58 UTC] Raw URL: judge_management/searchUsers
[11-Jul-2025 11:36:58 UTC] Processed URL segments: Array
(
    [0] => judge_management
    [1] => searchUsers
)

[11-Jul-2025 11:36:58 UTC] URL parsed: Array
(
    [0] => judge_management
    [1] => searchUsers
)

[11-Jul-2025 11:36:58 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[11-Jul-2025 11:36:58 UTC] Database::resultSet - Result count: 36
[11-Jul-2025 11:36:58 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[11-Jul-2025 11:36:58 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[11-Jul-2025 11:36:58 UTC] App.php: Looking for method: searchUsers in controller: JudgeManagementController
[11-Jul-2025 11:36:58 UTC] App.php: Using camelCase method: searchUsers for URL: searchUsers
[11-Jul-2025 11:36:58 UTC] Controller: JudgeManagementController, Method: searchUsers, Params: Array
(
)

[11-Jul-2025 11:36:58 UTC] App.php: About to call method: searchUsers on controller: JudgeManagementController
[11-Jul-2025 11:36:58 UTC] Database::resultSet - Executing query: SQL: [645] 
                SELECT id, name, email, phone, role
                FROM users
                WHERE status = "active"
                    AND (
                        name LIKE :query1 OR
                        email LIKE :query2 OR
                        phone LIKE :query3
                    )
                    AND role IN ("judge", "admin", "coordinator")
                ORDER BY
                    CASE
                        WHEN name LIKE :query4 THEN 1
                        WHEN email LIKE :query5 THEN 2
                        ELSE 3
                    END,
                    name
                LIMIT 10
            
Params:  5
Key: Name: [7] :query1
paramno=0
name=[7] ":query1"
is_param=1
param_type=2
Key: Name: [7] :query2
paramno=1
name=[7] ":query2"
is_param=1
param_type=2
Key: Name: [7] :query3
paramno=2
name=[7] ":query3"
is_param=1
param_type=2
Key: Name: [7] :query4
paramno=3
name=[7] ":query4"
is_param=1
param_type=2
Key: Name: [7] :query5
paramno=4
name=[7] ":query5"
is_param=1
param_type=2

[11-Jul-2025 11:36:58 UTC] Database::resultSet - Result count: 2
[11-Jul-2025 11:36:58 UTC] Database::resultSet - First result: {"id":"3","name":"Brian Correll","email":"<EMAIL>","phone":"7042023114","role":"admin"}
[11-Jul-2025 11:37:05 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:05 UTC] Raw URL: judge_management/assignJudge
[11-Jul-2025 11:37:05 UTC] Processed URL segments: Array
(
    [0] => judge_management
    [1] => assignJudge
)

[11-Jul-2025 11:37:05 UTC] URL parsed: Array
(
    [0] => judge_management
    [1] => assignJudge
)

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 36
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[11-Jul-2025 11:37:05 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[11-Jul-2025 11:37:05 UTC] App.php: Looking for method: assignJudge in controller: JudgeManagementController
[11-Jul-2025 11:37:05 UTC] App.php: Using camelCase method: assignJudge for URL: assignJudge
[11-Jul-2025 11:37:05 UTC] Controller: JudgeManagementController, Method: assignJudge, Params: Array
(
)

[11-Jul-2025 11:37:05 UTC] App.php: About to call method: assignJudge on controller: JudgeManagementController
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [23] SHOW COLUMNS FROM shows
Params:  0

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 23
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"}
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [90] SELECT field_id, field_value, field_type FROM custom_field_values WHERE show_id = :show_id
Params:  1
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=1

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 15
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"field_id":"address1","field_value":"232 N Main Street","field_type":"text"}
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Found 15 custom field values in custom_field_values table
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field address1 has type text and value length: 17
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field address2 has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field chk has type checkbox and value length: 2
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field city has type text and value length: 9
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field club has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field datandtime has type datetime and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field drp has type select and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field eee has type radio and value length: 1
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field email has type email and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field field_1749561018636 has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field jsdfhsdf has type textarea and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field rich has type richtext and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field state has type select and value length: 2
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field test has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field zipcode has type text and value length: 5
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [81] SELECT * FROM show_categories WHERE show_id = :show_id ORDER BY display_order ASC
Params:  1
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=1

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"id":"152","show_id":"9","name":"Classic Cars (Pre-1960)","description":"Vehicles manufactured before 1960","registration_fee":"0.00","max_entries":"0","created_at":"2025-06-08 13:38:33","updated_at":"2025-06-08 13:38:33","is_active":"1","requires_judging":"1","display_order":"0"}
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [23] SHOW COLUMNS FROM shows
Params:  0

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 23
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"}
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [90] SELECT field_id, field_value, field_type FROM custom_field_values WHERE show_id = :show_id
Params:  1
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=1

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 15
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"field_id":"address1","field_value":"232 N Main Street","field_type":"text"}
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Found 15 custom field values in custom_field_values table
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field address1 has type text and value length: 17
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field address2 has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field chk has type checkbox and value length: 2
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field city has type text and value length: 9
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field club has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field datandtime has type datetime and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field drp has type select and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field eee has type radio and value length: 1
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field email has type email and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field field_1749561018636 has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field jsdfhsdf has type textarea and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field rich has type richtext and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field state has type select and value length: 2
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field test has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field zipcode has type text and value length: 5
[11-Jul-2025 11:37:05 UTC] PHP Warning:  Undefined property: stdClass::$event_date in /home/<USER>/events.rowaneliterides.com/controllers/JudgeManagementController.php on line 912
[11-Jul-2025 11:37:05 UTC] PHP Deprecated:  strtotime(): Passing null to parameter #1 ($datetime) of type string is deprecated in /home/<USER>/events.rowaneliterides.com/controllers/JudgeManagementController.php on line 912
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:05 UTC] NotificationService: Email <NAME_EMAIL> - Success
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:05 UTC] NotificationService: Push notification stored for user 3
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:05 UTC] NotificationService: Toast notification stored for user 3
[11-Jul-2025 11:37:05 UTC] JudgeManagementController::sendMultiTypeNotification - Sent 3 notifications to user 3
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:05 UTC] NotificationService: Email <NAME_EMAIL> - Success
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:05 UTC] NotificationService: Push notification stored for user 5
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:05 UTC] NotificationService: Toast notification stored for user 5
[11-Jul-2025 11:37:05 UTC] JudgeManagementController::sendMultiTypeNotification - Sent 3 notifications to user 5
[11-Jul-2025 11:37:05 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:05 UTC] Raw URL: judge_management/index/9
[11-Jul-2025 11:37:05 UTC] Processed URL segments: Array
(
    [0] => judge_management
    [1] => index
    [2] => 9
)

[11-Jul-2025 11:37:05 UTC] URL parsed: Array
(
    [0] => judge_management
    [1] => index
    [2] => 9
)

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 36
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[11-Jul-2025 11:37:05 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[11-Jul-2025 11:37:05 UTC] App.php: Looking for method: index in controller: JudgeManagementController
[11-Jul-2025 11:37:05 UTC] App.php: Using camelCase method: index for URL: index
[11-Jul-2025 11:37:05 UTC] Controller: JudgeManagementController, Method: index, Params: Array
(
    [0] => 9
)

[11-Jul-2025 11:37:05 UTC] App.php: About to call method: index on controller: JudgeManagementController
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [23] SHOW COLUMNS FROM shows
Params:  0

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 23
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"}
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [90] SELECT field_id, field_value, field_type FROM custom_field_values WHERE show_id = :show_id
Params:  1
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=1

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 15
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"field_id":"address1","field_value":"232 N Main Street","field_type":"text"}
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Found 15 custom field values in custom_field_values table
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field address1 has type text and value length: 17
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field address2 has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field chk has type checkbox and value length: 2
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field city has type text and value length: 9
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field club has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field datandtime has type datetime and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field drp has type select and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field eee has type radio and value length: 1
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field email has type email and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field field_1749561018636 has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field jsdfhsdf has type textarea and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field rich has type richtext and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field state has type select and value length: 2
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field test has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field zipcode has type text and value length: 5
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [1393] 
                SELECT DISTINCT
                    u.id as user_id,
                    u.name as judge_name,
                    u.email as judge_email,
                    u.phone as judge_phone,
                    j.is_active as role_active,
                    j.created_at as role_assigned_at,
                    j.denial_note,
                    GROUP_CONCAT(DISTINCT sc.name ORDER BY sc.name) as assigned_categories,
                    GROUP_CONCAT(DISTINCT jc.category_id ORDER BY jc.category_id) as category_ids,
                    COUNT(DISTINCT jc.id) as category_count,
                    CASE
                        WHEN j.id IS NOT NULL AND j.is_active = 1 THEN "approved"
                        WHEN j.id IS NOT NULL AND j.is_active = 0 THEN "pending"
                        WHEN j.id IS NOT NULL AND j.is_active = -1 THEN "denied"
                        ELSE "not_assigned"
                    END as status
                FROM users u
                INNER JOIN judges j ON u.id = j.user_id AND j.show_id = :show_id
                LEFT JOIN judge_categories jc ON j.id = jc.judge_id
                LEFT JOIN show_categories sc ON jc.category_id = sc.id
                WHERE u.role IN ("judge", "admin", "coordinator")
                GROUP BY u.id, u.name, u.email, u.phone, j.is_active, j.created_at, j.denial_note
                ORDER BY u.name
            
Params:  1
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=2

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 1
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"user_id":"3","judge_name":"Brian Correll","judge_email":"<EMAIL>","judge_phone":"7042023114","role_active":"0","role_assigned_at":"2025-07-11 11:37:05","denial_note":null,"assigned_categories":"Asian Imports,Classic Cars (Pre-1960),Contemporary (2000-Present),European Imports,Modern Classics (1980-1999),Modified\/Custom,Motorcycles,Muscle Cars (1960-1979),Trucks &amp; SUVs","category_ids":"152,153,154,155,156,157,158,159,160","category_count":"9","status":"pending"}
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [81] SELECT * FROM show_categories WHERE show_id = :show_id ORDER BY display_order ASC
Params:  1
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=2

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"id":"152","show_id":"9","name":"Classic Cars (Pre-1960)","description":"Vehicles manufactured before 1960","registration_fee":"0.00","max_entries":"0","created_at":"2025-06-08 13:38:33","updated_at":"2025-06-08 13:38:33","is_active":"1","requires_judging":"1","display_order":"0"}
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [23] SHOW COLUMNS FROM shows
Params:  0

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 23
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"}
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [90] SELECT field_id, field_value, field_type FROM custom_field_values WHERE show_id = :show_id
Params:  1
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=1

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 15
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"field_id":"address1","field_value":"232 N Main Street","field_type":"text"}
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Found 15 custom field values in custom_field_values table
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field address1 has type text and value length: 17
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field address2 has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field chk has type checkbox and value length: 2
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field city has type text and value length: 9
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field club has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field datandtime has type datetime and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field drp has type select and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field eee has type radio and value length: 1
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field email has type email and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field field_1749561018636 has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field jsdfhsdf has type textarea and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field rich has type richtext and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field state has type select and value length: 2
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field test has type text and value length: 0
[11-Jul-2025 11:37:05 UTC] ShowModel::getShowById - Field zipcode has type text and value length: 5
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [484] 
                SELECT u.id, u.name, u.email, u.phone, u.role
                FROM users u
                WHERE u.role IN ("judge", "admin", "coordinator")
                    AND u.status = "active"
                    AND u.id NOT IN (
                        SELECT j.user_id
                        FROM judges j
                        WHERE j.show_id = :show_id
                            AND j.is_active = 1
                    )
                ORDER BY u.name
            
Params:  1
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=2

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 6
[11-Jul-2025 11:37:05 UTC] Database::resultSet - First result: {"id":"5","name":"Admin","email":"<EMAIL>","phone":null,"role":"coordinator"}
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [1152] 
                SELECT
                    j.user_id as judge_id,
                    u.name as judge_name,
                    jc.category_id,
                    sc.name as category_name,
                    COUNT(DISTINCT r.id) as vehicles_to_judge,
                    COUNT(DISTINCT CASE WHEN s.id IS NOT NULL AND s.is_draft = 0 THEN r.id END) as vehicles_judged,
                    COUNT(DISTINCT CASE WHEN s.id IS NOT NULL AND s.is_draft = 1 THEN r.id END) as vehicles_draft
                FROM judges j
                JOIN users u ON j.user_id = u.id
                LEFT JOIN judge_categories jc ON j.id = jc.judge_id
                LEFT JOIN show_categories sc ON jc.category_id = sc.id
                LEFT JOIN registrations r ON (jc.category_id IS NULL OR r.category_id = jc.category_id)
                    AND r.show_id = :show_id AND r.status = "approved"
                LEFT JOIN scores s ON r.id = s.registration_id AND s.judge_id = j.user_id
                WHERE j.show_id = :show_id2 AND j.is_active = 1
                GROUP BY j.user_id, jc.category_id, u.name, sc.name
                ORDER BY u.name, sc.name
            
Params:  2
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=2
Key: Name: [9] :show_id2
paramno=1
name=[9] ":show_id2"
is_param=1
param_type=2

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 0
[11-Jul-2025 11:37:05 UTC] Database::resultSet - No results found
[11-Jul-2025 11:37:05 UTC] Database::resultSet - Executing query: SQL: [293] SELECT * FROM notification_queue 
                             WHERE user_id = :user_id 
                             AND status = :status 
                             AND scheduled_for <= NOW()
                             ORDER BY created_at DESC 
                             LIMIT 50
Params:  2
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2
Key: Name: [7] :status
paramno=1
name=[7] ":status"
is_param=1
param_type=2

[11-Jul-2025 11:37:05 UTC] Database::resultSet - Result count: 0
[11-Jul-2025 11:37:05 UTC] Database::resultSet - No results found
[11-Jul-2025 11:37:06 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:06 UTC] Raw URL: api/getSiteLogo
[11-Jul-2025 11:37:06 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => getSiteLogo
)

[11-Jul-2025 11:37:06 UTC] URL parsed: Array
(
    [0] => api
    [1] => getSiteLogo
)

[11-Jul-2025 11:37:06 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => getSiteLogo
)

[11-Jul-2025 11:37:06 UTC] [API_ROUTING] Endpoint: getSiteLogo, Action: index
[11-Jul-2025 11:37:06 UTC] [API] Calling ApiController::getSiteLogo
[11-Jul-2025 11:37:06 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:06 UTC] Raw URL: api/cameraBanners
[11-Jul-2025 11:37:06 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => cameraBanners
)

[11-Jul-2025 11:37:06 UTC] URL parsed: Array
(
    [0] => api
    [1] => cameraBanners
)

[11-Jul-2025 11:37:06 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => cameraBanners
)

[11-Jul-2025 11:37:06 UTC] [API_ROUTING] Endpoint: cameraBanners, Action: index
[11-Jul-2025 11:37:06 UTC] [CAMERA_BANNERS_API] Handler called with action: index
[11-Jul-2025 11:37:06 UTC] [CAMERA_BANNERS_API] Calling cameraBanners method
[11-Jul-2025 11:37:06 UTC] [CAMERA_BANNERS_API] API endpoint called
[11-Jul-2025 11:37:06 UTC] Database::resultSet - Executing query: SQL: [86] SELECT * FROM camera_banners WHERE active = 1 ORDER BY sort_order ASC, created_at DESC
Params:  0

[11-Jul-2025 11:37:06 UTC] Database::resultSet - Result count: 4
[11-Jul-2025 11:37:06 UTC] Database::resultSet - First result: {"id":"16","type":"text","text":"Banner 1: Welcome to our Event Platform!","image_path":null,"alt_text":null,"active":"1","sort_order":"1","created_at":"2025-07-06 18:58:49","updated_at":"2025-07-06 18:58:49"}
[11-Jul-2025 11:37:06 UTC] [CAMERA_BANNERS_API] Returning 5 banners
[11-Jul-2025 11:37:06 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:06 UTC] Raw URL: notification/getUnread
[11-Jul-2025 11:37:06 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[11-Jul-2025 11:37:06 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[11-Jul-2025 11:37:06 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[11-Jul-2025 11:37:06 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[11-Jul-2025 11:37:06 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[11-Jul-2025 11:37:06 UTC] App.php: About to call method: getUnread on controller: NotificationController
[11-Jul-2025 11:37:06 UTC] Database::resultSet - Executing query: SQL: [293] SELECT * FROM notification_queue 
                             WHERE user_id = :user_id 
                             AND status = :status 
                             AND scheduled_for <= NOW()
                             ORDER BY created_at DESC 
                             LIMIT 50
Params:  2
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2
Key: Name: [7] :status
paramno=1
name=[7] ":status"
is_param=1
param_type=2

[11-Jul-2025 11:37:06 UTC] Database::resultSet - Result count: 0
[11-Jul-2025 11:37:06 UTC] Database::resultSet - No results found
[11-Jul-2025 11:37:06 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:06 UTC] Raw URL: api/pwa/usage
[11-Jul-2025 11:37:06 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => pwa
    [2] => usage
)

[11-Jul-2025 11:37:06 UTC] URL parsed: Array
(
    [0] => api
    [1] => pwa
    [2] => usage
)

[11-Jul-2025 11:37:06 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => pwa
    [1] => usage
)

[11-Jul-2025 11:37:06 UTC] [API_ROUTING] Endpoint: pwa, Action: usage
[11-Jul-2025 11:37:06 UTC] [PWA] VAPID keys initialized - Public key available: Yes
[11-Jul-2025 11:37:06 UTC] [PWA] Usage data received for user 3: {"isInstalled":false,"isStandalone":false,"supportsPush":true,"timestamp":"2025-07-11T11:37:04.307Z"}
[11-Jul-2025 11:37:06 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:06 UTC] Raw URL: api/notifications/subscribe
[11-Jul-2025 11:37:06 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => notifications
    [2] => subscribe
)

[11-Jul-2025 11:37:06 UTC] URL parsed: Array
(
    [0] => api
    [1] => notifications
    [2] => subscribe
)

[11-Jul-2025 11:37:06 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => notifications
    [1] => subscribe
)

[11-Jul-2025 11:37:06 UTC] [API_ROUTING] Endpoint: notifications, Action: subscribe
[11-Jul-2025 11:37:06 UTC] [PWA] VAPID keys initialized - Public key available: Yes
[11-Jul-2025 11:37:06 UTC] [PWA] Push subscription saved for user 3
[11-Jul-2025 11:37:06 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:06 UTC] Raw URL: api/cameraBanners
[11-Jul-2025 11:37:06 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => cameraBanners
)

[11-Jul-2025 11:37:06 UTC] URL parsed: Array
(
    [0] => api
    [1] => cameraBanners
)

[11-Jul-2025 11:37:06 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => cameraBanners
)

[11-Jul-2025 11:37:06 UTC] [API_ROUTING] Endpoint: cameraBanners, Action: index
[11-Jul-2025 11:37:06 UTC] [CAMERA_BANNERS_API] Handler called with action: index
[11-Jul-2025 11:37:06 UTC] [CAMERA_BANNERS_API] Calling cameraBanners method
[11-Jul-2025 11:37:06 UTC] [CAMERA_BANNERS_API] API endpoint called
[11-Jul-2025 11:37:06 UTC] Database::resultSet - Executing query: SQL: [86] SELECT * FROM camera_banners WHERE active = 1 ORDER BY sort_order ASC, created_at DESC
Params:  0

[11-Jul-2025 11:37:06 UTC] Database::resultSet - Result count: 4
[11-Jul-2025 11:37:06 UTC] Database::resultSet - First result: {"id":"16","type":"text","text":"Banner 1: Welcome to our Event Platform!","image_path":null,"alt_text":null,"active":"1","sort_order":"1","created_at":"2025-07-06 18:58:49","updated_at":"2025-07-06 18:58:49"}
[11-Jul-2025 11:37:06 UTC] [CAMERA_BANNERS_API] Returning 5 banners
[11-Jul-2025 11:37:15 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:15 UTC] Raw URL: judge_management/approveJudgeRequest/9/3
[11-Jul-2025 11:37:15 UTC] Processed URL segments: Array
(
    [0] => judge_management
    [1] => approveJudgeRequest
    [2] => 9
    [3] => 3
)

[11-Jul-2025 11:37:15 UTC] URL parsed: Array
(
    [0] => judge_management
    [1] => approveJudgeRequest
    [2] => 9
    [3] => 3
)

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 36
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[11-Jul-2025 11:37:15 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[11-Jul-2025 11:37:15 UTC] App.php: Looking for method: approveJudgeRequest in controller: JudgeManagementController
[11-Jul-2025 11:37:15 UTC] App.php: Using camelCase method: approveJudgeRequest for URL: approveJudgeRequest
[11-Jul-2025 11:37:15 UTC] Controller: JudgeManagementController, Method: approveJudgeRequest, Params: Array
(
    [0] => 9
    [1] => 3
)

[11-Jul-2025 11:37:15 UTC] App.php: About to call method: approveJudgeRequest on controller: JudgeManagementController
[11-Jul-2025 11:37:15 UTC] JudgeManagementController::approveJudgeRequest - Called with showId: 9, judgeId: 3
[11-Jul-2025 11:37:15 UTC] JudgeManagementController::approveJudgeRequest - Found judge: Brian Correll, current status: 0
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [23] SHOW COLUMNS FROM shows
Params:  0

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 23
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"}
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [90] SELECT field_id, field_value, field_type FROM custom_field_values WHERE show_id = :show_id
Params:  1
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=1

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 15
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"field_id":"address1","field_value":"232 N Main Street","field_type":"text"}
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Found 15 custom field values in custom_field_values table
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field address1 has type text and value length: 17
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field address2 has type text and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field chk has type checkbox and value length: 2
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field city has type text and value length: 9
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field club has type text and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field datandtime has type datetime and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field drp has type select and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field eee has type radio and value length: 1
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field email has type email and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field field_1749561018636 has type text and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field jsdfhsdf has type textarea and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field rich has type richtext and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field state has type select and value length: 2
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field test has type text and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field zipcode has type text and value length: 5
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:15 UTC] NotificationService: Email <NAME_EMAIL> - Success
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:15 UTC] NotificationService: Push notification stored for user 3
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:15 UTC] NotificationService: Toast notification stored for user 3
[11-Jul-2025 11:37:15 UTC] JudgeManagementController::sendMultiTypeNotification - Sent 3 notifications to user 3
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:15 UTC] NotificationService: Email <NAME_EMAIL> - Success
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:15 UTC] NotificationService: Push notification stored for user 5
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [74] SELECT setting_key, setting_value, setting_type FROM notification_settings
Params:  0

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"setting_key":"email_enabled","setting_value":"1","setting_type":"boolean"}
[11-Jul-2025 11:37:15 UTC] NotificationService: Toast notification stored for user 5
[11-Jul-2025 11:37:15 UTC] JudgeManagementController::sendMultiTypeNotification - Sent 3 notifications to user 5
[11-Jul-2025 11:37:15 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:15 UTC] Raw URL: judge_management/index/9
[11-Jul-2025 11:37:15 UTC] Processed URL segments: Array
(
    [0] => judge_management
    [1] => index
    [2] => 9
)

[11-Jul-2025 11:37:15 UTC] URL parsed: Array
(
    [0] => judge_management
    [1] => index
    [2] => 9
)

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [63] SELECT form_field_id, db_column, field_type FROM field_mappings
Params:  0

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 36
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"form_field_id":"field_1748214173466","db_column":"field_1748214173466","field_type":"text"}
[11-Jul-2025 11:37:15 UTC] DynamicFormFieldManager: Loaded 36 mappings from database
[11-Jul-2025 11:37:15 UTC] App.php: Looking for method: index in controller: JudgeManagementController
[11-Jul-2025 11:37:15 UTC] App.php: Using camelCase method: index for URL: index
[11-Jul-2025 11:37:15 UTC] Controller: JudgeManagementController, Method: index, Params: Array
(
    [0] => 9
)

[11-Jul-2025 11:37:15 UTC] App.php: About to call method: index on controller: JudgeManagementController
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [23] SHOW COLUMNS FROM shows
Params:  0

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 23
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"}
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [90] SELECT field_id, field_value, field_type FROM custom_field_values WHERE show_id = :show_id
Params:  1
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=1

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 15
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"field_id":"address1","field_value":"232 N Main Street","field_type":"text"}
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Found 15 custom field values in custom_field_values table
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field address1 has type text and value length: 17
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field address2 has type text and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field chk has type checkbox and value length: 2
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field city has type text and value length: 9
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field club has type text and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field datandtime has type datetime and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field drp has type select and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field eee has type radio and value length: 1
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field email has type email and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field field_1749561018636 has type text and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field jsdfhsdf has type textarea and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field rich has type richtext and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field state has type select and value length: 2
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field test has type text and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field zipcode has type text and value length: 5
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [1393] 
                SELECT DISTINCT
                    u.id as user_id,
                    u.name as judge_name,
                    u.email as judge_email,
                    u.phone as judge_phone,
                    j.is_active as role_active,
                    j.created_at as role_assigned_at,
                    j.denial_note,
                    GROUP_CONCAT(DISTINCT sc.name ORDER BY sc.name) as assigned_categories,
                    GROUP_CONCAT(DISTINCT jc.category_id ORDER BY jc.category_id) as category_ids,
                    COUNT(DISTINCT jc.id) as category_count,
                    CASE
                        WHEN j.id IS NOT NULL AND j.is_active = 1 THEN "approved"
                        WHEN j.id IS NOT NULL AND j.is_active = 0 THEN "pending"
                        WHEN j.id IS NOT NULL AND j.is_active = -1 THEN "denied"
                        ELSE "not_assigned"
                    END as status
                FROM users u
                INNER JOIN judges j ON u.id = j.user_id AND j.show_id = :show_id
                LEFT JOIN judge_categories jc ON j.id = jc.judge_id
                LEFT JOIN show_categories sc ON jc.category_id = sc.id
                WHERE u.role IN ("judge", "admin", "coordinator")
                GROUP BY u.id, u.name, u.email, u.phone, j.is_active, j.created_at, j.denial_note
                ORDER BY u.name
            
Params:  1
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=2

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 1
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"user_id":"3","judge_name":"Brian Correll","judge_email":"<EMAIL>","judge_phone":"7042023114","role_active":"1","role_assigned_at":"2025-07-11 11:37:05","denial_note":null,"assigned_categories":"Asian Imports,Classic Cars (Pre-1960),Contemporary (2000-Present),European Imports,Modern Classics (1980-1999),Modified\/Custom,Motorcycles,Muscle Cars (1960-1979),Trucks &amp; SUVs","category_ids":"152,153,154,155,156,157,158,159,160","category_count":"9","status":"approved"}
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [81] SELECT * FROM show_categories WHERE show_id = :show_id ORDER BY display_order ASC
Params:  1
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=2

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"id":"152","show_id":"9","name":"Classic Cars (Pre-1960)","description":"Vehicles manufactured before 1960","registration_fee":"0.00","max_entries":"0","created_at":"2025-06-08 13:38:33","updated_at":"2025-06-08 13:38:33","is_active":"1","requires_judging":"1","display_order":"0"}
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [23] SHOW COLUMNS FROM shows
Params:  0

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 23
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"Field":"id","Type":"int(10) unsigned","Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment"}
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [90] SELECT field_id, field_value, field_type FROM custom_field_values WHERE show_id = :show_id
Params:  1
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=1

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 15
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"field_id":"address1","field_value":"232 N Main Street","field_type":"text"}
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Found 15 custom field values in custom_field_values table
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field address1 has type text and value length: 17
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field address2 has type text and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field chk has type checkbox and value length: 2
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field city has type text and value length: 9
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field club has type text and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field datandtime has type datetime and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field drp has type select and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field eee has type radio and value length: 1
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field email has type email and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field field_1749561018636 has type text and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field jsdfhsdf has type textarea and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field rich has type richtext and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field state has type select and value length: 2
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field test has type text and value length: 0
[11-Jul-2025 11:37:15 UTC] ShowModel::getShowById - Field zipcode has type text and value length: 5
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [484] 
                SELECT u.id, u.name, u.email, u.phone, u.role
                FROM users u
                WHERE u.role IN ("judge", "admin", "coordinator")
                    AND u.status = "active"
                    AND u.id NOT IN (
                        SELECT j.user_id
                        FROM judges j
                        WHERE j.show_id = :show_id
                            AND j.is_active = 1
                    )
                ORDER BY u.name
            
Params:  1
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=2

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 5
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"id":"5","name":"Admin","email":"<EMAIL>","phone":null,"role":"coordinator"}
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [1152] 
                SELECT
                    j.user_id as judge_id,
                    u.name as judge_name,
                    jc.category_id,
                    sc.name as category_name,
                    COUNT(DISTINCT r.id) as vehicles_to_judge,
                    COUNT(DISTINCT CASE WHEN s.id IS NOT NULL AND s.is_draft = 0 THEN r.id END) as vehicles_judged,
                    COUNT(DISTINCT CASE WHEN s.id IS NOT NULL AND s.is_draft = 1 THEN r.id END) as vehicles_draft
                FROM judges j
                JOIN users u ON j.user_id = u.id
                LEFT JOIN judge_categories jc ON j.id = jc.judge_id
                LEFT JOIN show_categories sc ON jc.category_id = sc.id
                LEFT JOIN registrations r ON (jc.category_id IS NULL OR r.category_id = jc.category_id)
                    AND r.show_id = :show_id AND r.status = "approved"
                LEFT JOIN scores s ON r.id = s.registration_id AND s.judge_id = j.user_id
                WHERE j.show_id = :show_id2 AND j.is_active = 1
                GROUP BY j.user_id, jc.category_id, u.name, sc.name
                ORDER BY u.name, sc.name
            
Params:  2
Key: Name: [8] :show_id
paramno=0
name=[8] ":show_id"
is_param=1
param_type=2
Key: Name: [9] :show_id2
paramno=1
name=[9] ":show_id2"
is_param=1
param_type=2

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 9
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"judge_id":"3","judge_name":"Brian Correll","category_id":"157","category_name":"Asian Imports","vehicles_to_judge":"0","vehicles_judged":"0","vehicles_draft":"0"}
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [293] SELECT * FROM notification_queue 
                             WHERE user_id = :user_id 
                             AND status = :status 
                             AND scheduled_for <= NOW()
                             ORDER BY created_at DESC 
                             LIMIT 50
Params:  2
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2
Key: Name: [7] :status
paramno=1
name=[7] ":status"
is_param=1
param_type=2

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 0
[11-Jul-2025 11:37:15 UTC] Database::resultSet - No results found
[11-Jul-2025 11:37:15 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:15 UTC] Raw URL: api/getSiteLogo
[11-Jul-2025 11:37:15 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => getSiteLogo
)

[11-Jul-2025 11:37:15 UTC] URL parsed: Array
(
    [0] => api
    [1] => getSiteLogo
)

[11-Jul-2025 11:37:15 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => getSiteLogo
)

[11-Jul-2025 11:37:15 UTC] [API_ROUTING] Endpoint: getSiteLogo, Action: index
[11-Jul-2025 11:37:15 UTC] [API] Calling ApiController::getSiteLogo
[11-Jul-2025 11:37:15 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:15 UTC] Raw URL: api/cameraBanners
[11-Jul-2025 11:37:15 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => cameraBanners
)

[11-Jul-2025 11:37:15 UTC] URL parsed: Array
(
    [0] => api
    [1] => cameraBanners
)

[11-Jul-2025 11:37:15 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => cameraBanners
)

[11-Jul-2025 11:37:15 UTC] [API_ROUTING] Endpoint: cameraBanners, Action: index
[11-Jul-2025 11:37:15 UTC] [CAMERA_BANNERS_API] Handler called with action: index
[11-Jul-2025 11:37:15 UTC] [CAMERA_BANNERS_API] Calling cameraBanners method
[11-Jul-2025 11:37:15 UTC] [CAMERA_BANNERS_API] API endpoint called
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [86] SELECT * FROM camera_banners WHERE active = 1 ORDER BY sort_order ASC, created_at DESC
Params:  0

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 4
[11-Jul-2025 11:37:15 UTC] Database::resultSet - First result: {"id":"16","type":"text","text":"Banner 1: Welcome to our Event Platform!","image_path":null,"alt_text":null,"active":"1","sort_order":"1","created_at":"2025-07-06 18:58:49","updated_at":"2025-07-06 18:58:49"}
[11-Jul-2025 11:37:15 UTC] [CAMERA_BANNERS_API] Returning 5 banners
[11-Jul-2025 11:37:15 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:15 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:15 UTC] Raw URL: notification/getUnread
[11-Jul-2025 11:37:15 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[11-Jul-2025 11:37:15 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[11-Jul-2025 11:37:15 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[11-Jul-2025 11:37:15 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[11-Jul-2025 11:37:15 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[11-Jul-2025 11:37:15 UTC] App.php: About to call method: getUnread on controller: NotificationController
[11-Jul-2025 11:37:15 UTC] Database::resultSet - Executing query: SQL: [293] SELECT * FROM notification_queue 
                             WHERE user_id = :user_id 
                             AND status = :status 
                             AND scheduled_for <= NOW()
                             ORDER BY created_at DESC 
                             LIMIT 50
Params:  2
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2
Key: Name: [7] :status
paramno=1
name=[7] ":status"
is_param=1
param_type=2

[11-Jul-2025 11:37:15 UTC] Database::resultSet - Result count: 0
[11-Jul-2025 11:37:15 UTC] Database::resultSet - No results found
[11-Jul-2025 11:37:15 UTC] Raw URL: api/pwa/usage
[11-Jul-2025 11:37:15 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => pwa
    [2] => usage
)

[11-Jul-2025 11:37:15 UTC] URL parsed: Array
(
    [0] => api
    [1] => pwa
    [2] => usage
)

[11-Jul-2025 11:37:15 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => pwa
    [1] => usage
)

[11-Jul-2025 11:37:15 UTC] [API_ROUTING] Endpoint: pwa, Action: usage
[11-Jul-2025 11:37:15 UTC] [PWA] VAPID keys initialized - Public key available: Yes
[11-Jul-2025 11:37:15 UTC] [PWA] Usage data received for user 3: {"isInstalled":false,"isStandalone":false,"supportsPush":true,"timestamp":"2025-07-11T11:37:14.166Z"}
[11-Jul-2025 11:37:15 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:15 UTC] Raw URL: api/notifications/subscribe
[11-Jul-2025 11:37:15 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => notifications
    [2] => subscribe
)

[11-Jul-2025 11:37:15 UTC] URL parsed: Array
(
    [0] => api
    [1] => notifications
    [2] => subscribe
)

[11-Jul-2025 11:37:15 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => notifications
    [1] => subscribe
)

[11-Jul-2025 11:37:15 UTC] [API_ROUTING] Endpoint: notifications, Action: subscribe
[11-Jul-2025 11:37:15 UTC] [PWA] VAPID keys initialized - Public key available: Yes
[11-Jul-2025 11:37:15 UTC] [PWA] Push subscription saved for user 3
[11-Jul-2025 11:37:16 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:16 UTC] Raw URL: api/cameraBanners
[11-Jul-2025 11:37:16 UTC] Processed URL segments: Array
(
    [0] => api
    [1] => cameraBanners
)

[11-Jul-2025 11:37:16 UTC] URL parsed: Array
(
    [0] => api
    [1] => cameraBanners
)

[11-Jul-2025 11:37:16 UTC] [API_ROUTING] URL after removing api: Array
(
    [0] => cameraBanners
)

[11-Jul-2025 11:37:16 UTC] [API_ROUTING] Endpoint: cameraBanners, Action: index
[11-Jul-2025 11:37:16 UTC] [CAMERA_BANNERS_API] Handler called with action: index
[11-Jul-2025 11:37:16 UTC] [CAMERA_BANNERS_API] Calling cameraBanners method
[11-Jul-2025 11:37:16 UTC] [CAMERA_BANNERS_API] API endpoint called
[11-Jul-2025 11:37:16 UTC] Database::resultSet - Executing query: SQL: [86] SELECT * FROM camera_banners WHERE active = 1 ORDER BY sort_order ASC, created_at DESC
Params:  0

[11-Jul-2025 11:37:16 UTC] Database::resultSet - Result count: 4
[11-Jul-2025 11:37:16 UTC] Database::resultSet - First result: {"id":"16","type":"text","text":"Banner 1: Welcome to our Event Platform!","image_path":null,"alt_text":null,"active":"1","sort_order":"1","created_at":"2025-07-06 18:58:49","updated_at":"2025-07-06 18:58:49"}
[11-Jul-2025 11:37:16 UTC] [CAMERA_BANNERS_API] Returning 5 banners
[11-Jul-2025 11:37:23 UTC] Session lifetime from database: 2592000 seconds
[11-Jul-2025 11:37:23 UTC] Raw URL: notification/getUnread
[11-Jul-2025 11:37:23 UTC] Processed URL segments: Array
(
    [0] => notification
    [1] => getUnread
)

[11-Jul-2025 11:37:23 UTC] URL parsed: Array
(
    [0] => notification
    [1] => getUnread
)

[11-Jul-2025 11:37:23 UTC] App.php: Looking for method: getUnread in controller: NotificationController
[11-Jul-2025 11:37:23 UTC] App.php: Using camelCase method: getUnread for URL: getUnread
[11-Jul-2025 11:37:23 UTC] Controller: NotificationController, Method: getUnread, Params: Array
(
)

[11-Jul-2025 11:37:23 UTC] App.php: About to call method: getUnread on controller: NotificationController
[11-Jul-2025 11:37:23 UTC] Database::resultSet - Executing query: SQL: [293] SELECT * FROM notification_queue 
                             WHERE user_id = :user_id 
                             AND status = :status 
                             AND scheduled_for <= NOW()
                             ORDER BY created_at DESC 
                             LIMIT 50
Params:  2
Key: Name: [8] :user_id
paramno=0
name=[8] ":user_id"
is_param=1
param_type=2
Key: Name: [7] :status
paramno=1
name=[7] ":status"
is_param=1
param_type=2

[11-Jul-2025 11:37:23 UTC] Database::resultSet - Result count: 0
[11-Jul-2025 11:37:23 UTC] Database::resultSet - No results found