<?php

/**
 * ShowRoleModel
 *
 * Handles per-show role assignments with approval workflow
 * Allows users to maintain their primary role while having temporary show-specific roles
 *
 * @version 1.0
 * @date 2025-07-10
 */
class ShowRoleModel {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    /**
     * Request a role assignment for a user on a specific show
     *
     * @param int $showId Show ID
     * @param int $userId User ID to assign role to
     * @param string $role Role to assign (coordinator, judge, staff)
     * @param int $requestedBy User ID of person making the request
     * @param string $message Optional message to include with request
     * @param bool $isAdminAssignment If true, skip approval workflow (admin assignment)
     * @return bool|int Request ID on success, false on failure
     */
    public function requestRoleAssignment($showId, $userId, $role, $requestedBy, $message = null, $isAdminAssignment = false) {
        try {
            // Validate role
            $validRoles = ['coordinator', 'judge', 'staff'];
            if (!in_array($role, $validRoles)) {
                return false;
            }

            // Check if user exists
            if (!$this->userExists($userId)) {
                return false;
            }

            // Check if show exists
            if (!$this->showExists($showId)) {
                return false;
            }

            // Check if there's already a pending request for this user/show/role
            if ($this->hasPendingRequest($showId, $userId, $role)) {
                return false;
            }

            // Check if user already has this role for this show
            if ($this->hasActiveAssignment($showId, $userId, $role)) {
                return false;
            }

            // If admin assignment, create assignment directly
            if ($isAdminAssignment) {
                return $this->createDirectAssignment($showId, $userId, $role, $requestedBy, $message);
            }

            // Set expiration date (7 days from now)
            $expiresAt = date('Y-m-d H:i:s', strtotime('+7 days'));

            // Create the request
            $this->db->query('INSERT INTO show_role_requests
                              (show_id, user_id, requested_role, requested_by, request_message, expires_at)
                              VALUES (:show_id, :user_id, :role, :requested_by, :message, :expires_at)');

            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':role', $role);
            $this->db->bind(':requested_by', $requestedBy);
            $this->db->bind(':message', $message);
            $this->db->bind(':expires_at', $expiresAt);

            if ($this->db->execute()) {
                $requestId = $this->db->lastInsertId();

                // Send notification to user
                $this->sendRoleRequestNotification($requestId);

                return $requestId;
            }

            return false;

        } catch (Exception $e) {
            error_log("ShowRoleModel::requestRoleAssignment - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create a direct assignment (admin bypass)
     *
     * @param int $showId Show ID
     * @param int $userId User ID
     * @param string $role Role to assign
     * @param int $assignedBy Admin user ID
     * @param string $message Optional message
     * @return bool|int Assignment ID on success, false on failure
     */
    public function createDirectAssignment($showId, $userId, $role, $assignedBy, $message = null) {
        try {
            // Get show end date for expiration calculation
            $this->db->query('SELECT end_date FROM shows WHERE id = :show_id');
            $this->db->bind(':show_id', $showId);
            $show = $this->db->single();

            if (!$show) {
                return false;
            }

            // Calculate expiration dates
            $expiresAt = date('Y-m-d 23:59:59', strtotime($show->end_date . ' +1 day'));
            $autoCleanupDate = date('Y-m-d 23:59:59', strtotime($show->end_date . ' +1 week'));

            // Create assignment
            $this->db->query('INSERT INTO show_role_assignments
                              (show_id, user_id, assigned_role, assigned_by, expires_at, auto_cleanup_date)
                              VALUES (:show_id, :user_id, :role, :assigned_by, :expires_at, :cleanup_date)');

            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':role', $role);
            $this->db->bind(':assigned_by', $assignedBy);
            $this->db->bind(':expires_at', $expiresAt);
            $this->db->bind(':cleanup_date', $autoCleanupDate);

            if ($this->db->execute()) {
                $assignmentId = $this->db->lastInsertId();

                // Send notification to user about direct assignment
                $this->sendDirectAssignmentNotification($assignmentId);

                return $assignmentId;
            }

            return false;

        } catch (Exception $e) {
            error_log("ShowRoleModel::createDirectAssignment - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Respond to a role assignment request
     *
     * @param int $requestId Request ID
     * @param int $userId User ID responding (must match request user_id)
     * @param string $response 'approved' or 'declined'
     * @param string $message Optional response message
     * @return bool Success status
     */
    public function respondToRequest($requestId, $userId, $response, $message = null) {
        try {
            // Validate response
            if (!in_array($response, ['approved', 'declined'])) {
                return false;
            }

            // Get the request
            $request = $this->getRequest($requestId);
            if (!$request || $request->user_id != $userId || $request->status != 'pending') {
                return false;
            }

            // Check if request has expired
            if (strtotime($request->expires_at) < time()) {
                // Mark as expired
                $this->markRequestExpired($requestId);
                return false;
            }

            // Update request status
            $this->db->query('UPDATE show_role_requests
                              SET status = :status, response_message = :message, responded_at = NOW()
                              WHERE id = :request_id');

            $this->db->bind(':status', $response);
            $this->db->bind(':message', $message);
            $this->db->bind(':request_id', $requestId);

            if (!$this->db->execute()) {
                return false;
            }

            // If approved, create the assignment
            if ($response === 'approved') {
                $assignmentId = $this->createAssignment($request, $requestId);
                if (!$assignmentId) {
                    // Rollback the request status update
                    $this->db->query('UPDATE show_role_requests SET status = "pending", responded_at = NULL WHERE id = :request_id');
                    $this->db->bind(':request_id', $requestId);
                    $this->db->execute();
                    return false;
                }
            }

            // Send notification to requester
            $this->sendResponseNotification($requestId, $response);

            return true;

        } catch (Exception $e) {
            error_log("ShowRoleModel::respondToRequest - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Remove a role assignment
     *
     * @param int $assignmentId Assignment ID
     * @param int $removedBy User ID of person removing the assignment
     * @return bool Success status
     */
    public function removeAssignment($assignmentId, $removedBy) {
        try {
            // Get assignment details for notification
            $this->db->query('SELECT sra.*, u.name as user_name, u.email as user_email, s.name as show_name
                              FROM show_role_assignments sra
                              JOIN users u ON sra.user_id = u.id
                              JOIN shows s ON sra.show_id = s.id
                              WHERE sra.id = :assignment_id AND sra.is_active = 1');
            $this->db->bind(':assignment_id', $assignmentId);
            $assignment = $this->db->single();

            if (!$assignment) {
                return false;
            }

            // Deactivate the assignment
            $this->db->query('UPDATE show_role_assignments
                              SET is_active = 0, updated_at = NOW()
                              WHERE id = :assignment_id');
            $this->db->bind(':assignment_id', $assignmentId);

            if ($this->db->execute()) {
                // Send notification to user about removal
                $this->sendRemovalNotification($assignment, $removedBy);
                return true;
            }

            return false;

        } catch (Exception $e) {
            error_log("ShowRoleModel::removeAssignment - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Search users for assignment
     *
     * @param string $searchTerm Search term (name, email, or user ID)
     * @param int $limit Maximum results to return
     * @return array Array of user results
     */
    public function searchUsers($searchTerm, $limit = 20) {
        try {
            $searchTerm = trim($searchTerm);

            if (empty($searchTerm)) {
                return [];
            }

            // Check if search term is numeric (user ID search)
            if (is_numeric($searchTerm)) {
                $this->db->query('SELECT id, name, email, role, created_at
                                  FROM users
                                  WHERE id = :user_id AND status = "active"
                                  LIMIT 1');
                $this->db->bind(':user_id', (int)$searchTerm);
            } else {
                // Text search on name and email
                $searchPattern = '%' . $searchTerm . '%';
                $this->db->query('SELECT id, name, email, role, created_at
                                  FROM users
                                  WHERE (name LIKE :search OR email LIKE :search)
                                  AND status = "active"
                                  ORDER BY name ASC
                                  LIMIT :limit');
                $this->db->bind(':search', $searchPattern);
                $this->db->bind(':limit', $limit);
            }

            return $this->db->resultSet();

        } catch (Exception $e) {
            error_log("ShowRoleModel::searchUsers - Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Create an active role assignment from an approved request
     * 
     * @param object $request Request object
     * @param int $requestId Request ID
     * @return bool|int Assignment ID on success, false on failure
     */
    private function createAssignment($request, $requestId) {
        try {
            // Get show end date for expiration calculation
            $this->db->query('SELECT end_date FROM shows WHERE id = :show_id');
            $this->db->bind(':show_id', $request->show_id);
            $show = $this->db->single();
            
            if (!$show) {
                return false;
            }
            
            // Calculate expiration dates
            $expiresAt = date('Y-m-d 23:59:59', strtotime($show->end_date . ' +1 day'));
            $autoCleanupDate = date('Y-m-d 23:59:59', strtotime($show->end_date . ' +1 week'));
            
            // Create assignment
            $this->db->query('INSERT INTO show_role_assignments 
                              (show_id, user_id, assigned_role, assigned_by, request_id, expires_at, auto_cleanup_date) 
                              VALUES (:show_id, :user_id, :role, :assigned_by, :request_id, :expires_at, :cleanup_date)');
            
            $this->db->bind(':show_id', $request->show_id);
            $this->db->bind(':user_id', $request->user_id);
            $this->db->bind(':role', $request->requested_role);
            $this->db->bind(':assigned_by', $request->requested_by);
            $this->db->bind(':request_id', $requestId);
            $this->db->bind(':expires_at', $expiresAt);
            $this->db->bind(':cleanup_date', $autoCleanupDate);
            
            if ($this->db->execute()) {
                return $this->db->lastInsertId();
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("ShowRoleModel::createAssignment - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get user's show-specific roles for a show
     * 
     * @param int $userId User ID
     * @param int $showId Show ID
     * @return array Array of roles
     */
    public function getUserShowRoles($userId, $showId) {
        try {
            $this->db->query('SELECT assigned_role FROM show_role_assignments 
                              WHERE user_id = :user_id AND show_id = :show_id AND is_active = 1 AND expires_at > NOW()');
            
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':show_id', $showId);
            
            $results = $this->db->resultSet();
            
            return array_column($results, 'assigned_role');
            
        } catch (Exception $e) {
            error_log("ShowRoleModel::getUserShowRoles - Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get all active assignments for a show
     * 
     * @param int $showId Show ID
     * @return array Array of assignments with user details
     */
    public function getShowAssignments($showId) {
        try {
            $this->db->query('SELECT sra.*, u.name as user_name, u.email as user_email, 
                              assigner.name as assigned_by_name
                              FROM show_role_assignments sra
                              JOIN users u ON sra.user_id = u.id
                              JOIN users assigner ON sra.assigned_by = assigner.id
                              WHERE sra.show_id = :show_id AND sra.is_active = 1
                              ORDER BY sra.assigned_role, u.name');
            
            $this->db->bind(':show_id', $showId);
            
            return $this->db->resultSet();
            
        } catch (Exception $e) {
            error_log("ShowRoleModel::getShowAssignments - Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get pending requests for a user
     *
     * @param int $userId User ID
     * @return array Array of pending requests
     */
    public function getUserPendingRequests($userId) {
        try {
            $this->db->query('SELECT srr.*, s.name as show_name, s.start_date, s.end_date,
                              requester.name as requested_by_name
                              FROM show_role_requests srr
                              JOIN shows s ON srr.show_id = s.id
                              JOIN users requester ON srr.requested_by = requester.id
                              WHERE srr.user_id = :user_id AND srr.status = "pending" AND srr.expires_at > NOW()
                              ORDER BY srr.requested_at DESC');

            $this->db->bind(':user_id', $userId);

            return $this->db->resultSet();

        } catch (Exception $e) {
            error_log("ShowRoleModel::getUserPendingRequests - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get all assignments for a user
     *
     * @param int $userId User ID
     * @return array Array of assignments
     */
    public function getUserAssignments($userId) {
        try {
            $this->db->query('SELECT sra.*, s.name as show_name, s.start_date, s.end_date,
                              assigner.name as assigned_by_name
                              FROM show_role_assignments sra
                              JOIN shows s ON sra.show_id = s.id
                              JOIN users assigner ON sra.assigned_by = assigner.id
                              WHERE sra.user_id = :user_id AND sra.is_active = 1
                              ORDER BY s.start_date DESC');

            $this->db->bind(':user_id', $userId);

            return $this->db->resultSet();

        } catch (Exception $e) {
            error_log("ShowRoleModel::getUserAssignments - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Clean up expired assignments (for cron job)
     *
     * @return array Cleanup results
     */
    public function cleanupExpiredAssignments() {
        try {
            $results = [
                'expired_assignments' => 0,
                'expired_requests' => 0,
                'errors' => []
            ];

            // Clean up expired assignments
            $this->db->query('UPDATE show_role_assignments
                              SET is_active = 0, updated_at = NOW()
                              WHERE auto_cleanup_date <= NOW() AND is_active = 1');

            if ($this->db->execute()) {
                $results['expired_assignments'] = $this->db->rowCount();
            }

            // Clean up expired requests
            $this->db->query('UPDATE show_role_requests
                              SET status = "expired", updated_at = NOW()
                              WHERE expires_at <= NOW() AND status = "pending"');

            if ($this->db->execute()) {
                $results['expired_requests'] = $this->db->rowCount();
            }

            return $results;

        } catch (Exception $e) {
            error_log("ShowRoleModel::cleanupExpiredAssignments - Error: " . $e->getMessage());
            return ['expired_assignments' => 0, 'expired_requests' => 0, 'errors' => [$e->getMessage()]];
        }
    }

    // Helper methods

    /**
     * Check if user exists
     *
     * @param int $userId User ID
     * @return bool
     */
    private function userExists($userId) {
        $this->db->query('SELECT COUNT(*) as count FROM users WHERE id = :user_id AND status = "active"');
        $this->db->bind(':user_id', $userId);
        $result = $this->db->single();
        return $result && $result->count > 0;
    }

    /**
     * Check if show exists
     *
     * @param int $showId Show ID
     * @return bool
     */
    private function showExists($showId) {
        $this->db->query('SELECT COUNT(*) as count FROM shows WHERE id = :show_id');
        $this->db->bind(':show_id', $showId);
        $result = $this->db->single();
        return $result && $result->count > 0;
    }

    /**
     * Check if user has pending request for role
     *
     * @param int $showId Show ID
     * @param int $userId User ID
     * @param string $role Role
     * @return bool
     */
    private function hasPendingRequest($showId, $userId, $role) {
        $this->db->query('SELECT COUNT(*) as count FROM show_role_requests
                          WHERE show_id = :show_id AND user_id = :user_id AND requested_role = :role
                          AND status = "pending" AND expires_at > NOW()');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':role', $role);
        $result = $this->db->single();
        return $result && $result->count > 0;
    }

    /**
     * Check if user has active assignment for role
     *
     * @param int $showId Show ID
     * @param int $userId User ID
     * @param string $role Role
     * @return bool
     */
    private function hasActiveAssignment($showId, $userId, $role) {
        $this->db->query('SELECT COUNT(*) as count FROM show_role_assignments
                          WHERE show_id = :show_id AND user_id = :user_id AND assigned_role = :role
                          AND is_active = 1 AND expires_at > NOW()');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':role', $role);
        $result = $this->db->single();
        return $result && $result->count > 0;
    }

    /**
     * Get a request by ID
     *
     * @param int $requestId Request ID
     * @return object|false Request object or false
     */
    private function getRequest($requestId) {
        $this->db->query('SELECT * FROM show_role_requests WHERE id = :request_id');
        $this->db->bind(':request_id', $requestId);
        return $this->db->single();
    }

    /**
     * Mark request as expired
     *
     * @param int $requestId Request ID
     * @return bool
     */
    private function markRequestExpired($requestId) {
        $this->db->query('UPDATE show_role_requests SET status = "expired", updated_at = NOW() WHERE id = :request_id');
        $this->db->bind(':request_id', $requestId);
        return $this->db->execute();
    }



    // Notification methods

    /**
     * Send role request notification to user
     *
     * @param int $requestId Request ID
     * @return bool
     */
    private function sendRoleRequestNotification($requestId) {
        try {
            // Get request details
            $this->db->query('SELECT srr.*, s.name as show_name, s.start_date, s.end_date,
                              u.name as user_name, u.email as user_email,
                              requester.name as requester_name
                              FROM show_role_requests srr
                              JOIN shows s ON srr.show_id = s.id
                              JOIN users u ON srr.user_id = u.id
                              JOIN users requester ON srr.requested_by = requester.id
                              WHERE srr.id = :request_id');
            $this->db->bind(':request_id', $requestId);
            $request = $this->db->single();

            if (!$request) {
                return false;
            }

            // Create notification
            $subject = "Show Role Assignment Request - {$request->show_name}";
            $message = "Hello {$request->user_name},\n\n";
            $message .= "{$request->requester_name} has requested to assign you the role of '{$request->requested_role}' ";
            $message .= "for the show '{$request->show_name}' (Date: " . date('M j, Y', strtotime($request->start_date)) . ").\n\n";

            if ($request->request_message) {
                $message .= "Message: {$request->request_message}\n\n";
            }

            $message .= "Please log in to your account to accept or decline this assignment.\n";
            $message .= "This request will expire on " . date('M j, Y g:i A', strtotime($request->expires_at)) . ".\n\n";
            $message .= "Thank you!";

            // Queue notification
            return $this->queueNotification($request->user_id, 'email', $subject, $message, [
                'type' => 'role_request',
                'request_id' => $requestId,
                'show_id' => $request->show_id
            ]);

        } catch (Exception $e) {
            error_log("ShowRoleModel::sendRoleRequestNotification - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send direct assignment notification to user
     *
     * @param int $assignmentId Assignment ID
     * @return bool
     */
    private function sendDirectAssignmentNotification($assignmentId) {
        try {
            // Get assignment details
            $this->db->query('SELECT sra.*, s.name as show_name, s.start_date, s.end_date,
                              u.name as user_name, u.email as user_email,
                              assigner.name as assigner_name
                              FROM show_role_assignments sra
                              JOIN shows s ON sra.show_id = s.id
                              JOIN users u ON sra.user_id = u.id
                              JOIN users assigner ON sra.assigned_by = assigner.id
                              WHERE sra.id = :assignment_id');
            $this->db->bind(':assignment_id', $assignmentId);
            $assignment = $this->db->single();

            if (!$assignment) {
                return false;
            }

            // Create notification
            $subject = "Show Role Assignment - {$assignment->show_name}";
            $message = "Hello {$assignment->user_name},\n\n";
            $message .= "You have been assigned the role of '{$assignment->assigned_role}' ";
            $message .= "for the show '{$assignment->show_name}' (Date: " . date('M j, Y', strtotime($assignment->start_date)) . ") ";
            $message .= "by {$assignment->assigner_name}.\n\n";
            $message .= "This assignment is active immediately and will expire on " . date('M j, Y', strtotime($assignment->expires_at)) . ".\n\n";
            $message .= "You can now access the show management features for this role.\n\n";
            $message .= "Thank you!";

            // Queue notification
            return $this->queueNotification($assignment->user_id, 'email', $subject, $message, [
                'type' => 'direct_assignment',
                'assignment_id' => $assignmentId,
                'show_id' => $assignment->show_id
            ]);

        } catch (Exception $e) {
            error_log("ShowRoleModel::sendDirectAssignmentNotification - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send response notification to requester
     *
     * @param int $requestId Request ID
     * @param string $response Response (approved/declined)
     * @return bool
     */
    private function sendResponseNotification($requestId, $response) {
        try {
            // Get request details
            $this->db->query('SELECT srr.*, s.name as show_name,
                              u.name as user_name,
                              requester.name as requester_name, requester.email as requester_email
                              FROM show_role_requests srr
                              JOIN shows s ON srr.show_id = s.id
                              JOIN users u ON srr.user_id = u.id
                              JOIN users requester ON srr.requested_by = requester.id
                              WHERE srr.id = :request_id');
            $this->db->bind(':request_id', $requestId);
            $request = $this->db->single();

            if (!$request) {
                return false;
            }

            // Create notification
            $subject = "Role Assignment Request " . ucfirst($response) . " - {$request->show_name}";
            $message = "Hello {$request->requester_name},\n\n";
            $message .= "{$request->user_name} has {$response} your request to assign them the role of '{$request->requested_role}' ";
            $message .= "for the show '{$request->show_name}'.\n\n";

            if ($request->response_message) {
                $message .= "Their response: {$request->response_message}\n\n";
            }

            if ($response === 'approved') {
                $message .= "The role assignment is now active.\n\n";
            }

            $message .= "Thank you!";

            // Queue notification
            return $this->queueNotification($request->requested_by, 'email', $subject, $message, [
                'type' => 'request_response',
                'request_id' => $requestId,
                'show_id' => $request->show_id,
                'response' => $response
            ]);

        } catch (Exception $e) {
            error_log("ShowRoleModel::sendResponseNotification - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send removal notification to user
     *
     * @param object $assignment Assignment object
     * @param int $removedBy User ID who removed the assignment
     * @return bool
     */
    private function sendRemovalNotification($assignment, $removedBy) {
        try {
            // Get remover details
            $this->db->query('SELECT name FROM users WHERE id = :user_id');
            $this->db->bind(':user_id', $removedBy);
            $remover = $this->db->single();

            if (!$remover) {
                return false;
            }

            // Create notification
            $subject = "Show Role Assignment Removed - {$assignment->show_name}";
            $message = "Hello {$assignment->user_name},\n\n";
            $message .= "Your role assignment as '{$assignment->assigned_role}' ";
            $message .= "for the show '{$assignment->show_name}' has been removed by {$remover->name}.\n\n";
            $message .= "You no longer have access to the management features for this role.\n\n";
            $message .= "Thank you!";

            // Queue notification
            return $this->queueNotification($assignment->user_id, 'email', $subject, $message, [
                'type' => 'assignment_removal',
                'assignment_id' => $assignment->id,
                'show_id' => $assignment->show_id
            ]);

        } catch (Exception $e) {
            error_log("ShowRoleModel::sendRemovalNotification - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Queue a notification for sending
     *
     * @param int $userId User ID
     * @param string $type Notification type
     * @param string $subject Subject
     * @param string $message Message
     * @param array $data Additional data
     * @return bool
     */
    private function queueNotification($userId, $type, $subject, $message, $data = []) {
        try {
            // Use the current notification_queue table structure
            $this->db->query('INSERT INTO notification_queue
                              (user_id, notification_type, event_id, event_type, notification_category,
                               scheduled_for, subject, message, status)
                              VALUES (:user_id, :type, :event_id, :event_type, :category, NOW(), :subject, :message, "pending")');

            $this->db->bind(':user_id', $userId);
            $this->db->bind(':type', $type);
            $this->db->bind(':event_id', $data['show_id'] ?? 0);
            $this->db->bind(':event_type', 'car_show');
            $this->db->bind(':category', 'test'); // Use 'test' category since 'role_assignment' may not exist yet
            $this->db->bind(':subject', $subject);
            $this->db->bind(':message', $message);

            return $this->db->execute();

        } catch (Exception $e) {
            error_log("ShowRoleModel::queueNotification - Error: " . $e->getMessage());
            return false;
        }
    }
}
