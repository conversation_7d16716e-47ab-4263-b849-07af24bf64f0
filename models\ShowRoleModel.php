<?php

/**
 * ShowRoleModel
 * 
 * Handles per-show role assignments with approval workflow
 * Allows users to maintain their primary role while having temporary show-specific roles
 * 
 * @version 1.0
 * @date 2025-07-10
 */
class ShowRoleModel {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Request a role assignment for a user on a specific show
     * 
     * @param int $showId Show ID
     * @param int $userId User ID to assign role to
     * @param string $role Role to assign (coordinator, judge, staff)
     * @param int $requestedBy User ID of person making the request
     * @param string $message Optional message to include with request
     * @return bool|int Request ID on success, false on failure
     */
    public function requestRoleAssignment($showId, $userId, $role, $requestedBy, $message = null) {
        try {
            // Validate role
            $validRoles = ['coordinator', 'judge', 'staff'];
            if (!in_array($role, $validRoles)) {
                return false;
            }
            
            // Check if user exists
            if (!$this->userExists($userId)) {
                return false;
            }
            
            // Check if show exists
            if (!$this->showExists($showId)) {
                return false;
            }
            
            // Check if there's already a pending request for this user/show/role
            if ($this->hasPendingRequest($showId, $userId, $role)) {
                return false;
            }
            
            // Check if user already has this role for this show
            if ($this->hasActiveAssignment($showId, $userId, $role)) {
                return false;
            }
            
            // Set expiration date (7 days from now)
            $expiresAt = date('Y-m-d H:i:s', strtotime('+7 days'));
            
            // Create the request
            $this->db->query('INSERT INTO show_role_requests 
                              (show_id, user_id, requested_role, requested_by, request_message, expires_at) 
                              VALUES (:show_id, :user_id, :role, :requested_by, :message, :expires_at)');
            
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':role', $role);
            $this->db->bind(':requested_by', $requestedBy);
            $this->db->bind(':message', $message);
            $this->db->bind(':expires_at', $expiresAt);
            
            if ($this->db->execute()) {
                $requestId = $this->db->lastInsertId();
                
                // Send notification to user
                $this->sendRoleRequestNotification($requestId);
                
                return $requestId;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("ShowRoleModel::requestRoleAssignment - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Respond to a role assignment request
     * 
     * @param int $requestId Request ID
     * @param int $userId User ID responding (must match request user_id)
     * @param string $response 'approved' or 'declined'
     * @param string $message Optional response message
     * @return bool Success status
     */
    public function respondToRequest($requestId, $userId, $response, $message = null) {
        try {
            // Validate response
            if (!in_array($response, ['approved', 'declined'])) {
                return false;
            }
            
            // Get the request
            $request = $this->getRequest($requestId);
            if (!$request || $request->user_id != $userId || $request->status != 'pending') {
                return false;
            }
            
            // Check if request has expired
            if (strtotime($request->expires_at) < time()) {
                // Mark as expired
                $this->markRequestExpired($requestId);
                return false;
            }
            
            // Update request status
            $this->db->query('UPDATE show_role_requests 
                              SET status = :status, response_message = :message, responded_at = NOW() 
                              WHERE id = :request_id');
            
            $this->db->bind(':status', $response);
            $this->db->bind(':message', $message);
            $this->db->bind(':request_id', $requestId);
            
            if (!$this->db->execute()) {
                return false;
            }
            
            // If approved, create the assignment
            if ($response === 'approved') {
                $assignmentId = $this->createAssignment($request, $requestId);
                if (!$assignmentId) {
                    // Rollback the request status update
                    $this->db->query('UPDATE show_role_requests SET status = "pending", responded_at = NULL WHERE id = :request_id');
                    $this->db->bind(':request_id', $requestId);
                    $this->db->execute();
                    return false;
                }
            }
            
            // Send notification to requester
            $this->sendResponseNotification($requestId, $response);
            
            return true;
            
        } catch (Exception $e) {
            error_log("ShowRoleModel::respondToRequest - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create an active role assignment from an approved request
     * 
     * @param object $request Request object
     * @param int $requestId Request ID
     * @return bool|int Assignment ID on success, false on failure
     */
    private function createAssignment($request, $requestId) {
        try {
            // Get show end date for expiration calculation
            $this->db->query('SELECT end_date FROM shows WHERE id = :show_id');
            $this->db->bind(':show_id', $request->show_id);
            $show = $this->db->single();
            
            if (!$show) {
                return false;
            }
            
            // Calculate expiration dates
            $expiresAt = date('Y-m-d 23:59:59', strtotime($show->end_date . ' +1 day'));
            $autoCleanupDate = date('Y-m-d 23:59:59', strtotime($show->end_date . ' +1 week'));
            
            // Create assignment
            $this->db->query('INSERT INTO show_role_assignments 
                              (show_id, user_id, assigned_role, assigned_by, request_id, expires_at, auto_cleanup_date) 
                              VALUES (:show_id, :user_id, :role, :assigned_by, :request_id, :expires_at, :cleanup_date)');
            
            $this->db->bind(':show_id', $request->show_id);
            $this->db->bind(':user_id', $request->user_id);
            $this->db->bind(':role', $request->requested_role);
            $this->db->bind(':assigned_by', $request->requested_by);
            $this->db->bind(':request_id', $requestId);
            $this->db->bind(':expires_at', $expiresAt);
            $this->db->bind(':cleanup_date', $autoCleanupDate);
            
            if ($this->db->execute()) {
                return $this->db->lastInsertId();
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("ShowRoleModel::createAssignment - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get user's show-specific roles for a show
     * 
     * @param int $userId User ID
     * @param int $showId Show ID
     * @return array Array of roles
     */
    public function getUserShowRoles($userId, $showId) {
        try {
            $this->db->query('SELECT assigned_role FROM show_role_assignments 
                              WHERE user_id = :user_id AND show_id = :show_id AND is_active = 1 AND expires_at > NOW()');
            
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':show_id', $showId);
            
            $results = $this->db->resultSet();
            
            return array_column($results, 'assigned_role');
            
        } catch (Exception $e) {
            error_log("ShowRoleModel::getUserShowRoles - Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get all active assignments for a show
     * 
     * @param int $showId Show ID
     * @return array Array of assignments with user details
     */
    public function getShowAssignments($showId) {
        try {
            $this->db->query('SELECT sra.*, u.name as user_name, u.email as user_email, 
                              assigner.name as assigned_by_name
                              FROM show_role_assignments sra
                              JOIN users u ON sra.user_id = u.id
                              JOIN users assigner ON sra.assigned_by = assigner.id
                              WHERE sra.show_id = :show_id AND sra.is_active = 1
                              ORDER BY sra.assigned_role, u.name');
            
            $this->db->bind(':show_id', $showId);
            
            return $this->db->resultSet();
            
        } catch (Exception $e) {
            error_log("ShowRoleModel::getShowAssignments - Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get pending requests for a user
     * 
     * @param int $userId User ID
     * @return array Array of pending requests
     */
    public function getUserPendingRequests($userId) {
        try {
            $this->db->query('SELECT srr.*, s.name as show_name, s.start_date, s.end_date,
                              requester.name as requested_by_name
                              FROM show_role_requests srr
                              JOIN shows s ON srr.show_id = s.id
                              JOIN users requester ON srr.requested_by = requester.id
                              WHERE srr.user_id = :user_id AND srr.status = "pending" AND srr.expires_at > NOW()
                              ORDER BY srr.requested_at DESC');
            
            $this->db->bind(':user_id', $userId);
            
            return $this->db->resultSet();
            
        } catch (Exception $e) {
            error_log("ShowRoleModel::getUserPendingRequests - Error: " . $e->getMessage());
            return [];
        }
    }
