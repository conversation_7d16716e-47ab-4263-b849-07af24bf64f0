/**
 * Modal Z-Index Fixes
 * 
 * Fixes z-index conflicts between Bootstrap modals and racing-style header/navigation
 * This ensures modals always appear above all other site elements
 */

/* Bootstrap Modal Z-Index Fixes */
.modal {
    z-index: 10500 !important;
}

.modal-backdrop {
    z-index: 10499 !important;
}

.modal-dialog {
    z-index: 10501 !important;
    position: relative;
}

.modal.show {
    display: block !important;
    z-index: 10500 !important;
}

.modal-content {
    z-index: 10502 !important;
    position: relative;
}

/* Ensure modal header, body, and footer are properly layered */
.modal-header,
.modal-body,
.modal-footer {
    z-index: 10503 !important;
    position: relative;
}

/* Fix for any dropdowns inside modals */
.modal .dropdown-menu {
    z-index: 10504 !important;
}

/* Fix for any tooltips inside modals */
.modal .tooltip {
    z-index: 10505 !important;
}

/* Fix for any popovers inside modals */
.modal .popover {
    z-index: 10506 !important;
}

/* Ensure modal is always clickable */
.modal-dialog {
    pointer-events: auto !important;
}

.modal-content {
    pointer-events: auto !important;
}

/* Fix for racing navigation overlay conflicts */
.racing-overlay {
    z-index: 9999 !important; /* Keep below modals */
}

.racing-menu {
    z-index: 9998 !important; /* Keep below modals */
}

/* Fix for racing header dropdown conflicts */
.racing-header .dropdown-menu {
    z-index: 1050 !important; /* Keep below modals */
}

.racing-header .nav-item.dropdown.show {
    z-index: 1051 !important; /* Keep below modals */
}

/* Ensure modals work properly on mobile */
@media (max-width: 768px) {
    .modal {
        z-index: 10500 !important;
    }
    
    .modal-dialog {
        margin: 1rem !important;
        max-width: calc(100% - 2rem) !important;
    }
    
    /* Ensure mobile racing menu doesn't interfere */
    .racing-menu.show {
        z-index: 9998 !important;
    }
}

/* Fix for any custom dropdowns or overlays */
.dropdown-menu.show {
    z-index: 1050 !important; /* Keep below modals */
}

/* Fix for any toast notifications */
.toast-container {
    z-index: 10400 !important; /* Below modals but above other content */
}

/* Fix for any offcanvas elements */
.offcanvas {
    z-index: 10300 !important; /* Below modals */
}

.offcanvas-backdrop {
    z-index: 10299 !important; /* Below modals */
}

/* Debug helper - uncomment to see z-index issues */
/*
.modal {
    border: 3px solid red !important;
}

.modal-backdrop {
    background-color: rgba(255, 0, 0, 0.3) !important;
}
*/
