/**
 * Modal Z-Index and Accessibility Fixes
 *
 * Fixes z-index conflicts between Bootstrap modals and racing-style header/navigation
 * Also resolves Bootstrap aria-hidden accessibility warnings site-wide
 * This ensures modals always appear above all other site elements and are accessible
 */

/* Bootstrap Modal Z-Index Fixes - Conservative Approach */
.modal {
    z-index: 999999 !important;
}

.modal-backdrop {
    z-index: 999998 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
}

/* Ensure modal appears above backdrop */
.modal.show {
    z-index: 999999 !important;
    display: block !important;
}

/* Fix modal positioning without breaking existing layout */
.modal-dialog {
    z-index: 999999 !important;
    position: relative !important;
}

.modal-dialog {
    z-index: 999999 !important;
    position: relative !important;
    width: auto !important;
    margin: 1.75rem !important;
    pointer-events: none !important;
}

.modal.show {
    display: block !important;
    z-index: 999999 !important;
}

.modal-content {
    z-index: 999999 !important;
    position: relative !important;
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    pointer-events: auto !important;
    background-color: #fff !important;
    background-clip: padding-box !important;
    border: 1px solid rgba(0, 0, 0, 0.2) !important;
    border-radius: 0.375rem !important;
    outline: 0 !important;
}

/* Ensure modal header, body, and footer are properly layered */
.modal-header,
.modal-body,
.modal-footer {
    z-index: 100002 !important;
    position: relative;
}

/* Fix for any dropdowns inside modals */
.modal .dropdown-menu {
    z-index: 100003 !important;
}

/* Fix for any tooltips inside modals */
.modal .tooltip {
    z-index: 100004 !important;
}

/* Fix for any popovers inside modals */
.modal .popover {
    z-index: 100005 !important;
}

/* Ensure modal is always clickable and properly positioned */
.modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: auto !important;
}

.modal-dialog {
    pointer-events: auto !important;
    position: relative !important;
    margin: 1.75rem auto !important;
    max-width: 500px !important;
}

.modal-content {
    pointer-events: auto !important;
    position: relative !important;
    background-color: #fff !important;
    border: 1px solid rgba(0, 0, 0, 0.2) !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Force backdrop to cover everything */
.modal-backdrop {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
}

/* Aggressive fixes for racing header and navigation */
.racing-header {
    z-index: 100 !important; /* Force below modals */
}

.racing-overlay {
    z-index: 9999 !important; /* Keep below modals */
}

.racing-menu {
    z-index: 9998 !important; /* Keep below modals */
}

/* Fix for racing header dropdown conflicts */
.racing-header .dropdown-menu {
    z-index: 1050 !important; /* Keep below modals */
}

.racing-header .nav-item.dropdown.show {
    z-index: 1051 !important; /* Keep below modals */
}

/* Force all header elements to stay below modals */
.racing-header,
.racing-header *,
.navbar,
.navbar * {
    z-index: 100 !important;
}

/* When modal is open, ensure header stays behind */
body.modal-open .racing-header {
    z-index: 50 !important;
}

body.modal-open .racing-menu {
    z-index: 49 !important;
}

body.modal-open .racing-overlay {
    z-index: 48 !important;
}

/* Ensure modals work properly on mobile */
@media (max-width: 768px) {
    .modal {
        z-index: 99999 !important;
    }

    .modal-dialog {
        margin: 1rem !important;
        max-width: calc(100% - 2rem) !important;
        z-index: 100000 !important;
    }

    /* Ensure mobile racing menu doesn't interfere */
    .racing-menu.show {
        z-index: 9998 !important;
    }

    /* Force mobile header below modals */
    .racing-header {
        z-index: 50 !important;
    }
}

/* Fix for any custom dropdowns or overlays */
.dropdown-menu.show {
    z-index: 1050 !important; /* Keep below modals */
}

/* Fix for any toast notifications */
.toast-container {
    z-index: 10400 !important; /* Below modals but above other content */
}

/* Fix for any offcanvas elements */
.offcanvas {
    z-index: 10300 !important; /* Below modals */
}

.offcanvas-backdrop {
    z-index: 10299 !important; /* Below modals */
}

/* Nuclear option - force modals to appear above everything */
.modal.show {
    z-index: 999999 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    display: block !important;
}

.modal.show .modal-dialog {
    z-index: 999999 !important;
    position: relative !important;
    margin: 1.75rem auto !important;
}

.modal.show .modal-content {
    z-index: 999999 !important;
    position: relative !important;
}

.modal-backdrop.show {
    z-index: 999998 !important;
    opacity: 0.5 !important;
}

/* Conservative approach - just lower header z-index when modal is open */
body.modal-open .racing-header {
    z-index: 50 !important;
}

body.modal-open .racing-menu {
    z-index: 49 !important;
}

body.modal-open .racing-overlay {
    z-index: 48 !important;
}

/* Debug helper - uncomment to see z-index issues */
/*
.modal {
    border: 3px solid red !important;
}

.modal-backdrop {
    background-color: rgba(255, 0, 0, 0.3) !important;
}
*/
