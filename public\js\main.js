/**
 * Events and Shows Management System
 * Main JavaScript File
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add CSRF token to AJAX requests if meta tag exists
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        const token = csrfToken.getAttribute('content');
        
        // Add token to XMLHttpRequest (but exclude Google Maps URLs)
        const originalXhrOpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url) {
            const result = originalXhrOpen.apply(this, arguments);
            
            // Don't add CSRF token to Google Maps API requests
            if (url && !url.includes('googleapis.com') && !url.includes('gstatic.com')) {
                this.setRequestHeader('X-CSRF-TOKEN', token);
            }
            
            return result;
        };
        
        // Add token to fetch requests if they exist in browser (but exclude Google Maps URLs)
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = function(url, options = {}) {
                // Don't add CSRF token to Google Maps API requests
                if (url && (url.includes('googleapis.com') || url.includes('gstatic.com'))) {
                    return originalFetch.call(this, url, options);
                }
                
                if (!options.headers) {
                    options.headers = {};
                }
                
                // Convert Headers object to plain object if needed
                if (options.headers instanceof Headers) {
                    const plainHeaders = {};
                    for (const [key, value] of options.headers.entries()) {
                        plainHeaders[key] = value;
                    }
                    options.headers = plainHeaders;
                }
                
                options.headers['X-CSRF-TOKEN'] = token;
                return originalFetch.call(this, url, options);
            };
        }
    }
    
    // Initialize Bootstrap components properly
    function initializeBootstrapComponents() {
        if (typeof bootstrap !== 'undefined') {
            console.log('Initializing Bootstrap components');
            
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl, {
                    boundary: document.body // Ensure tooltips don't get cut off
                });
            });
            
            // Initialize popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl, {
                    boundary: document.body // Ensure popovers don't get cut off
                });
            });
            
            // Make sure all dropdown toggles have the proper attribute
            document.querySelectorAll('.dropdown-toggle').forEach(function(toggle) {
                if (!toggle.hasAttribute('data-bs-toggle')) {
                    toggle.setAttribute('data-bs-toggle', 'dropdown');
                }
            });
            
            // Fix for dropdowns in tables and other containers
            document.querySelectorAll('.table-responsive .dropdown-toggle, .card-body .dropdown-toggle').forEach(function(toggle) {
                toggle.setAttribute('data-bs-boundary', 'viewport');
                toggle.setAttribute('data-bs-display', 'static');
            });
            
            return true;
        } else {
            console.warn('Bootstrap is not defined. Attempting to load it dynamically.');
            
            // Create a script element to load Bootstrap
            const bootstrapScript = document.createElement('script');
            bootstrapScript.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js';
            bootstrapScript.integrity = 'sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN';
            bootstrapScript.crossOrigin = 'anonymous';
            
            // Initialize components after Bootstrap is loaded
            bootstrapScript.onload = function() {
                console.log('Bootstrap loaded successfully');
                setTimeout(initializeBootstrapComponents, 100); // Small delay to ensure Bootstrap is fully initialized
            };
            
            document.body.appendChild(bootstrapScript);
            return false;
        }
    }
    
    // Try to initialize Bootstrap components
    initializeBootstrapComponents();
    
    // Vehicle gallery thumbnail click handler
    const thumbnails = document.querySelectorAll('.vehicle-gallery .thumbnail');
    const mainImage = document.querySelector('.vehicle-gallery .main-image');
    
    if (thumbnails.length > 0 && mainImage) {
        thumbnails.forEach(thumbnail => {
            thumbnail.addEventListener('click', function() {
                const newSrc = this.getAttribute('src');
                mainImage.setAttribute('src', newSrc);
            });
        });
    }
    
    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
    
    // Judging score input validation
    const scoreInputs = document.querySelectorAll('.score-input');
    
    scoreInputs.forEach(input => {
        input.addEventListener('input', function() {
            const maxScore = parseFloat(this.getAttribute('max'));
            let value = parseFloat(this.value);
            
            if (isNaN(value)) {
                this.value = '';
            } else if (value < 0) {
                this.value = 0;
            } else if (value > maxScore) {
                this.value = maxScore;
            }
        });
    });
    
    // Delete confirmation
    const deleteButtons = document.querySelectorAll('.btn-delete');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(event) {
            if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                event.preventDefault();
            }
        });
    });
    
    // Image preview before upload
    const imageInput = document.getElementById('image-upload');
    const imagePreview = document.getElementById('image-preview');
    
    if (imageInput && imagePreview) {
        imageInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    imagePreview.setAttribute('src', e.target.result);
                    imagePreview.style.display = 'block';
                }
                
                reader.readAsDataURL(this.files[0]);
            }
        });
    }
    
    // Global datetime validation for all forms with datetime-local fields
    initializeGlobalDateTimeValidation();
    
    // Auto-dismiss alerts (but not permanent ones like selected user alerts)
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');

    alerts.forEach(alert => {
        setTimeout(() => {
            if (typeof bootstrap !== 'undefined') {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            } else {
                // Fallback if bootstrap is not available
                if (alert.parentNode) {
                    alert.classList.remove('show');
                    setTimeout(() => {
                        if (alert.parentNode) {
                            alert.parentNode.removeChild(alert);
                        }
                    }, 150);
                }
            }
        }, 5000);
    });
});

/**
 * Global DateTime Validation System
 * Handles datetime-local field validation across all forms
 */
function initializeGlobalDateTimeValidation() {
    console.log('Initializing global datetime validation...');
    
    // Find all forms that contain datetime-local fields
    const formsWithDateTime = document.querySelectorAll('form');
    
    formsWithDateTime.forEach(form => {
        const dateTimeFields = form.querySelectorAll('input[type="datetime-local"]');
        
        if (dateTimeFields.length > 0) {
            console.log('Found form with datetime fields:', form.id || form.action);
            
            // Add novalidate attribute to prevent HTML5 validation conflicts
            if (!form.hasAttribute('novalidate')) {
                form.setAttribute('novalidate', 'true');
                console.log('Added novalidate to form:', form.id || form.action);
            }
            
            // Add form submission validation
            form.addEventListener('submit', function(e) {
                console.log('Validating datetime fields in form:', form.id || form.action);
                
                if (!validateDateTimeFields(form)) {
                    e.preventDefault();
                    return false;
                }
                
                return true;
            });
        }
    });
}

/**
 * Validate datetime fields in a form
 * @param {HTMLFormElement} form - The form to validate
 * @returns {boolean} - True if validation passes, false otherwise
 */
function validateDateTimeFields(form) {
    let hasErrors = false;
    
    // Common datetime field pairs to validate
    const dateTimePairs = [
        { start: 'start_date', end: 'end_date', label: 'Event' },
        { start: 'registration_start', end: 'registration_end', label: 'Registration' },
        { start: 'registration_start_date', end: 'registration_end_date', label: 'Registration' }
    ];
    
    dateTimePairs.forEach(pair => {
        const startField = form.querySelector(`#${pair.start}, [name="${pair.start}"]`);
        const endField = form.querySelector(`#${pair.end}, [name="${pair.end}"]`);
        
        if (startField && endField && startField.value && endField.value) {
            console.log(`Validating ${pair.label} dates:`, {
                start: startField.value,
                end: endField.value
            });
            
            const startDate = new Date(startField.value);
            const endDate = new Date(endField.value);
            
            if (endDate <= startDate) {
                alert(`${pair.label} end date must be after ${pair.label.toLowerCase()} start date`);
                endField.focus();
                hasErrors = true;
                return; // Exit early on first error
            }
        }
    });
    
    // Additional validation: Registration must end before event starts
    const regEndField = form.querySelector('#registration_end, [name="registration_end"], #registration_end_date, [name="registration_end_date"]');
    const eventStartField = form.querySelector('#start_date, [name="start_date"]');
    
    if (!hasErrors && regEndField && eventStartField && regEndField.value && eventStartField.value) {
        const regEndDate = new Date(regEndField.value);
        const eventStartDate = new Date(eventStartField.value);
        
        if (regEndDate > eventStartDate) {
            alert('Registration must end before the event starts');
            regEndField.focus();
            hasErrors = true;
        }
    }
    
    return !hasErrors;
}