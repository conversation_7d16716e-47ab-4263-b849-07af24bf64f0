<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1><?php echo $title; ?></h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/admin/judging/<?php echo $show->id; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Judging
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <?php if(isset($_SESSION['flash_messages']['judging_message'])) : ?>
                <div class="<?php echo $_SESSION['flash_messages']['judging_message']['class']; ?>" role="alert">
                    <?php echo $_SESSION['flash_messages']['judging_message']['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['flash_messages']['judging_message']); ?>
            <?php endif; ?>
            
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="card-title mb-0">Category Information</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Category:</strong> <?php echo $category->name; ?></p>
                            <p><strong>Description:</strong> <?php echo $category->description; ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Show:</strong> <?php echo $show->name; ?></p>
                            <p><strong>Vehicles:</strong> <?php echo count($registrations); ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h4 class="card-title mb-0">Assigned Judges</h4>
                </div>
                <div class="card-body">
                    <?php if (empty($judges)) : ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i> No judges have been assigned to this category.
                        </div>
                        <a href="<?php echo BASE_URL; ?>/judge_management/index/<?php echo $show->id; ?>" class="btn btn-primary">
                            <i class="fas fa-users-cog me-1"></i> Judge Management
                        </a>
                    <?php else : ?>
                        <div class="row">
                            <?php foreach ($judges as $judge) : ?>
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <h5 class="card-title"><?php echo $judge->name; ?></h5>
                                            <p class="card-text">
                                                <small class="text-muted">Judge ID: <?php echo $judge->id; ?></small>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="card-title mb-0">Vehicles to Judge</h4>
                </div>
                <div class="card-body">
                    <?php if (empty($registrations)) : ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> No vehicles have been registered in this category.
                        </div>
                    <?php else : ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Vehicle</th>
                                        <th>Owner</th>
                                        <th>Registration #</th>
                                        <th>Judging Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($registrations as $registration) : ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if (!empty($registration->primary_image)) : ?>
                                                        <?php 
                                                        // Check if thumbnail exists
                                                        $imagePath = 'uploads/vehicles/' . $registration->primary_image;
                                                        $thumbnailPath = 'uploads/vehicles/thumbnails/' . $registration->primary_image;
                                                        $fullThumbnailPath = APPROOT . '/' . $thumbnailPath;
                                                        $useThumbnail = file_exists($fullThumbnailPath);
                                                        ?>
                                                        <img src="<?php echo BASE_URL; ?>/<?php echo $useThumbnail ? $thumbnailPath : $imagePath; ?>" 
                                                             class="me-2" alt="Vehicle" style="width: 50px; height: 40px; object-fit: cover;">
                                                    <?php else : ?>
                                                        <div class="me-2 bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 50px; height: 40px;">
                                                            <i class="fas fa-car"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <?php echo $registration->year; ?> <?php echo $registration->make; ?> <?php echo $registration->model; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo $registration->owner_name; ?></td>
                                            <td><?php echo $registration->registration_number; ?></td>
                                            <td>
                                                <?php 
                                                    $judgedCount = 0;
                                                    $totalJudges = count($judges);
                                                    
                                                    foreach ($judges as $judge) {
                                                        // Check if this judge has completed judging for this vehicle
                                                        if (isset($registration->judging_status[$judge->id]) && $registration->judging_status[$judge->id] === 'complete') {
                                                            $judgedCount++;
                                                        }
                                                    }
                                                    
                                                    if ($judgedCount === 0) {
                                                        echo '<span class="badge bg-danger">Not Judged</span>';
                                                    } elseif ($judgedCount < $totalJudges) {
                                                        echo '<span class="badge bg-warning">Partially Judged (' . $judgedCount . '/' . $totalJudges . ')</span>';
                                                    } else {
                                                        echo '<span class="badge bg-success">Fully Judged</span>';
                                                    }
                                                ?>
                                            </td>
                                            <td>
                                                <a href="<?php echo BASE_URL; ?>/admin/judgeVehicle/<?php echo $registration->id; ?>" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-clipboard-check me-1"></i> Judge
                                                </a>
                                                <a href="<?php echo BASE_URL; ?>/admin/viewScores/<?php echo $registration->id; ?>" class="btn btn-info btn-sm">
                                                    <i class="fas fa-chart-bar me-1"></i> Scores
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>