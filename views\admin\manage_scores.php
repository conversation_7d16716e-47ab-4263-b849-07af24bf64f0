<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Manage Scores</h1>
        <div>
            <a href="<?php echo BASE_URL; ?>/admin/scoringSettings" class="btn btn-primary me-2">
                <i class="fas fa-cog me-2"></i>Scoring Settings
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/viewLogs" class="btn btn-info me-2">
                <i class="fas fa-file-alt me-2"></i>View Scoring Logs
            </a>
            <a href="javascript:history.back();" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back
            </a>
        </div>
    </div>
    
    <?php if (isset($message) && !empty($message)): ?>
        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Select Show</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="<?php echo BASE_URL; ?>/admin/manageScores" class="row g-3">
                        <div class="col-md-8">
                            <select name="show_id" id="show_id" class="form-select" required>
                                <option value="">-- Select Show --</option>
                                <?php foreach ($shows as $show): ?>
                                    <option value="<?php echo $show->id; ?>" <?php echo (isset($selected_show) && $selected_show->id == $show->id) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($show->name); ?> 
                                        (<?php echo formatDateTimeForUser($show->start_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?>)
                                        - <?php echo ucfirst($show->status); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary">View Scores</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <?php if (isset($selected_show)): ?>
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">Score Summary for <?php echo htmlspecialchars($selected_show->name); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card h-100 border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="card-title mb-0">Judging Status</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Total Vehicles:</span>
                                        <span class="badge bg-primary"><?php echo $stats->total_vehicles; ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Vehicles Judged:</span>
                                        <span class="badge bg-success"><?php echo $stats->judged_vehicles; ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Vehicles Pending:</span>
                                        <span class="badge bg-warning"><?php echo $stats->pending_vehicles; ?></span>
                                    </div>
                                    <div class="progress mt-3" style="height: 20px;">
                                        <?php $judged_percent = ($stats->total_vehicles > 0) ? round(($stats->judged_vehicles / $stats->total_vehicles) * 100) : 0; ?>
                                        <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $judged_percent; ?>%;" aria-valuenow="<?php echo $judged_percent; ?>" aria-valuemin="0" aria-valuemax="100"><?php echo $judged_percent; ?>%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="card h-100 border-info">
                                <div class="card-header bg-info text-white">
                                    <h5 class="card-title mb-0">Score Statistics</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Highest Score:</span>
                                        <span class="badge bg-success"><?php echo number_format($stats->highest_score ?? 0, 2); ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Lowest Score:</span>
                                        <span class="badge bg-warning"><?php echo number_format($stats->lowest_score ?? 0, 2); ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Average Score:</span>
                                        <span class="badge bg-info"><?php echo number_format($stats->average_score ?? 0, 2); ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Total Scores:</span>
                                        <span class="badge bg-primary"><?php echo $stats->total_scores; ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="card h-100 border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="card-title mb-0">Scoring Settings</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Formula:</span>
                                        <span class="badge bg-primary"><?php echo htmlspecialchars($scoring_settings->formula_name ?? 'Default'); ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Weight Multiplier:</span>
                                        <span class="badge bg-info"><?php echo number_format($scoring_settings->weight_multiplier ?? 1.0, 2); ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Normalize Scores:</span>
                                        <span class="badge bg-<?php echo ($scoring_settings->normalize_scores ?? 0) ? 'success' : 'danger'; ?>">
                                            <?php echo ($scoring_settings->normalize_scores ?? 0) ? 'Yes' : 'No'; ?>
                                        </span>
                                    </div>
                                    <div class="mt-3">
                                        <a href="<?php echo BASE_URL; ?>/admin/scoringSettings#settings" class="btn btn-warning btn-sm">Edit Settings</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <ul class="nav nav-pills card-header-pills" id="scoresTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="vehicle-scores-tab" data-bs-toggle="tab" data-bs-target="#vehicle-scores" type="button" role="tab" aria-controls="vehicle-scores" aria-selected="true">Vehicle Scores</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="judge-scores-tab" data-bs-toggle="tab" data-bs-target="#judge-scores" type="button" role="tab" aria-controls="judge-scores" aria-selected="false">Judge Scores</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="category-winners-tab" data-bs-toggle="tab" data-bs-target="#category-winners" type="button" role="tab" aria-controls="category-winners" aria-selected="false">Category Winners</button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="scoresTabsContent">
                        <div class="tab-pane fade show active" id="vehicle-scores" role="tabpanel" aria-labelledby="vehicle-scores-tab">
                            <?php if (empty($vehicle_scores)): ?>
                                <div class="alert alert-info">
                                    No vehicle scores found for this show. Run the "Generate Vehicle Scores" calculation to create scores.
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="vehicleScoresTable">
                                        <thead>
                                            <tr>
                                                <th>Display #</th>
                                                <th>Vehicle</th>
                                                <th>Raw Score</th>
                                                <th>Weighted Score</th>
                                                <th>Age Weight</th>
                                                <th>Final Score</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($vehicle_scores as $score): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($score->display_number ?? 'N/A'); ?></td>
                                                    <td>
                                                        <?php echo htmlspecialchars($score->year . ' ' . $score->make . ' ' . $score->model); ?>
                                                    </td>
                                                    <td><?php echo number_format($score->raw_score ?? 0, 2); ?></td>
                                                    <td><?php echo number_format($score->weighted_score ?? 0, 2); ?></td>
                                                    <td><?php echo number_format($score->age_weight ?? 0, 2); ?></td>
                                                    <td><strong><?php echo number_format($score->total_score ?? 0, 2); ?></strong></td>
                                                    <td>
                                                        <a href="<?php echo BASE_URL; ?>/show/vehicleScore/<?php echo $data['selected_show']->id; ?>/<?php echo $score->vehicle_id; ?>" class="btn btn-sm btn-primary">View Details</a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="tab-pane fade" id="judge-scores" role="tabpanel" aria-labelledby="judge-scores-tab">
                            <?php if (empty($judge_scores)): ?>
                                <div class="alert alert-info">
                                    No judge scores found for this show. Run the "Generate Judge Scores" calculation to create scores.
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="judgeScoresTable">
                                        <thead>
                                            <tr>
                                                <th>Judge</th>
                                                <th>Vehicle</th>
                                                <th>Display #</th>
                                                <th>Raw Score</th>
                                                <th>Weighted Score</th>
                                                <th>Final Score</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($judge_scores as $score): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($score->judge_name); ?></td>
                                                    <td>
                                                        <?php echo htmlspecialchars($score->year . ' ' . $score->make . ' ' . $score->model); ?>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($score->display_number ?? 'N/A'); ?></td>
                                                    <td><?php echo number_format($score->raw_score ?? 0, 2); ?></td>
                                                    <td><?php echo number_format($score->weighted_score ?? 0, 2); ?></td>
                                                    <td><strong><?php echo number_format($score->final_score ?? 0, 2); ?></strong></td>
                                                    <td>
                                                        <a href="<?php echo BASE_URL; ?>/judge/viewScores/<?php echo $score->registration_id; ?>" class="btn btn-sm btn-primary">View Details</a>
                                                        <?php if ($selected_show->status !== 'completed'): ?>
                                                            <a href="<?php echo BASE_URL; ?>/adminScoring/editJudgeScore/<?php echo $score->id; ?>" class="btn btn-sm btn-warning">Edit Score</a>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="tab-pane fade" id="category-winners" role="tabpanel" aria-labelledby="category-winners-tab">
                            <?php if (empty($category_winners)): ?>
                                <div class="alert alert-info">
                                    No category winners found for this show. Run the "Calculate Category Winners" calculation to determine winners.
                                </div>
                            <?php else: ?>
                                <?php
                                // Group winners by category
                                $categorizedWinners = [];
                                foreach ($category_winners as $winner) {
                                    if (!isset($categorizedWinners[$winner->category_id])) {
                                        $categorizedWinners[$winner->category_id] = [
                                            'name' => $winner->category_name,
                                            'winners' => []
                                        ];
                                    }
                                    $categorizedWinners[$winner->category_id]['winners'][] = $winner;
                                }
                                ?>
                                
                                <div class="row">
                                    <?php foreach ($categorizedWinners as $categoryId => $category): ?>
                                    <div class="col-md-6 mb-4">
                                        <div class="card">
                                            <div class="card-header bg-info text-white">
                                                <h5 class="card-title mb-0"><?php echo htmlspecialchars($category['name']); ?></h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <?php foreach ($category['winners'] as $winner): ?>
                                                    <div class="col-md-12 mb-3">
                                                        <div class="card h-100 <?php echo ($winner->place == 1) ? 'border-warning' : (($winner->place == 2) ? 'border-secondary' : 'border-danger'); ?>">
                                                            <div class="card-header <?php echo ($winner->place == 1) ? 'bg-warning text-dark' : (($winner->place == 2) ? 'bg-secondary text-white' : 'bg-danger text-white'); ?>">
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <h6 class="mb-0">
                                                                        <?php if ($winner->place == 1): ?>
                                                                            <i class="fas fa-trophy text-dark"></i> 1st Place
                                                                        <?php elseif ($winner->place == 2): ?>
                                                                            <i class="fas fa-award"></i> 2nd Place
                                                                        <?php else: ?>
                                                                            <i class="fas fa-medal"></i> 3rd Place
                                                                        <?php endif; ?>
                                                                    </h6>
                                                                    <span class="badge <?php echo ($winner->place == 1) ? 'bg-dark' : (($winner->place == 2) ? 'bg-secondary' : 'bg-danger'); ?>">
                                                                        Score: <?php echo number_format($winner->score ?? 0, 2); ?>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            <div class="card-body">
                                                                <h5 class="card-title"><?php echo htmlspecialchars($winner->year . ' ' . $winner->make . ' ' . $winner->model); ?></h5>
                                                                <p class="card-text">
                                                                    <strong>Owner:</strong> <?php echo htmlspecialchars($winner->owner_name); ?><br>
                                                                    <strong>Display #:</strong> <?php echo htmlspecialchars($winner->display_number ?? 'N/A'); ?>
                                                                </p>
                                                                <a href="<?php echo BASE_URL; ?>/show/vehicleScore/<?php echo $data['selected_show']->id; ?>/<?php echo $winner->vehicle_id; ?>" class="btn btn-sm btn-primary">View Score Details</a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Extra padding at the bottom -->
    <div class="pb-5 mb-5"></div>
</div>

<script src="https://cdn.datatables.net/1.10.22/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.22/js/dataTables.bootstrap4.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTables
        $('#vehicleScoresTable, #judgeScoresTable').DataTable({
            "pageLength": 25,
            "order": [],
            "responsive": true
        });
        
        // Handle tab navigation via URL hash
        var hash = window.location.hash;
        if (hash) {
            $('#scoresTabs a[href="' + hash + '"]').tab('show');
        }
        
        // Update URL hash when tab changes
        $('#scoresTabs a').on('click', function (e) {
            $(this).tab('show');
            window.location.hash = $(this).attr('href');
        });
        
        // Add custom styles for badges
        $('<style>')
            .text(`
                .badge-silver {
                    background-color: #C0C0C0;
                    color: #000;
                }
                .badge-bronze {
                    background-color: #CD7F32;
                    color: #fff;
                }
            `)
            .appendTo('head');
    });
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>