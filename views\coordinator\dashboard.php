<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row mb-4 align-items-center">
        <div class="col-8 col-md-6">
            <h1 class="h2 mb-0">Coordinator Dashboard</h1>
            <p class="text-muted mb-0">Optimized for managing thousands of shows and registrations</p>
        </div>
        <div class="col-4 col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/coordinator/createShow" class="btn btn-primary me-2 d-none d-sm-inline">
                <i class="fas fa-plus me-2"></i> Create Show
            </a>
            <a href="<?php echo BASE_URL; ?>/coordinator/reports" class="btn btn-outline-primary">
                <i class="fas fa-chart-bar me-2 d-none d-sm-inline"></i> Reports
            </a>
        </div>
    </div>

    <?php flash('coordinator_message'); ?>

    <!-- Show Overview Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Show Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-primary shadow-sm show-overview-card" 
                                 data-filter="all" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-primary mb-2">All Shows</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($show_counts['total'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Total Shows</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-success shadow-sm show-overview-card" 
                                 data-filter="upcoming" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-success mb-2">Upcoming</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($show_counts['upcoming'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Upcoming Shows</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-warning shadow-sm show-overview-card" 
                                 data-filter="past" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-warning text-dark mb-2">Past</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($show_counts['past'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Past Shows</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-info shadow-sm show-overview-card" 
                                 data-filter="draft" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-info mb-2">Draft</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($show_counts['draft'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Draft Shows</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/coordinator/createShow" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                <span>Create New Show</span>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/coordinator/reports" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <span>Reports</span>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/coordinator/manageQrCodes" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-qrcode fa-2x mb-2"></i>
                                <span>QR Codes</span>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <div class="dropdown">
                                <button class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3 dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-users-cog fa-2x mb-2"></i>
                                    <span>Judge Management</span>
                                </button>
                                <ul class="dropdown-menu">
                                    <?php if (!empty($data['coordinator_shows'])): ?>
                                        <?php foreach (array_slice($data['coordinator_shows'], 0, 5) as $show): ?>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/judge_management/index/<?php echo $show->id; ?>">
                                                <i class="fas fa-users-cog me-2"></i><?= htmlspecialchars($show->name) ?>
                                            </a></li>
                                        <?php endforeach; ?>
                                        <?php if (count($data['coordinator_shows']) > 5): ?>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/shows">View All Shows</a></li>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <li><span class="dropdown-item-text text-muted">No shows available</span></li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Show Details Section (Lazy Loaded) -->
    <div class="show-section" id="show-section" style="display: none;">
        <div class="card">
            <div class="card-header bg-primary bg-opacity-25">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <span class="badge bg-primary me-2">Show Details</span>
                        <span class="badge bg-secondary" id="show-count-display">0</span>
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="closeShowSection()">
                            <i class="fas fa-times"></i> Close
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Search and Filter Controls -->
            <div class="card-body border-bottom">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="search-shows" class="form-label">Search Shows</label>
                        <input type="text" class="form-control" id="search-shows" 
                               placeholder="Search by name, location, or date...">
                    </div>
                    <div class="col-md-2">
                        <label for="status-filter" class="form-label">Status</label>
                        <select class="form-select" id="status-filter">
                            <option value="all">All Status</option>
                            <option value="upcoming">Upcoming</option>
                            <option value="past">Past</option>
                            <option value="draft">Draft</option>
                            <option value="published">Published</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="per-page-shows" class="form-label">Per Page</label>
                        <select class="form-select" id="per-page-shows">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="searchShows()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-secondary" onclick="clearShowSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-success" onclick="exportShows()">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <a href="<?php echo BASE_URL; ?>/coordinator/createShow" class="btn btn-primary">
                                <i class="fas fa-plus"></i> New Show
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Loading Indicator -->
            <div class="card-body text-center" id="loading-shows">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading shows...</p>
            </div>
            
            <!-- Shows Content (Will be populated via AJAX) -->
            <div id="shows-content" style="display: none;">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Lazy Loading -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show overview card click handlers
    document.querySelectorAll('.show-overview-card').forEach(card => {
        card.addEventListener('click', function() {
            const filter = this.dataset.filter;
            const count = parseInt(this.querySelector('.display-5').textContent.replace(/,/g, ''));

            if (count > 0) {
                loadShowSection(filter);
            }
        });
    });

    // Search input handlers
    document.getElementById('search-shows').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchShows();
        }
    });

    // Filter change handlers
    document.getElementById('status-filter').addEventListener('change', searchShows);
    document.getElementById('per-page-shows').addEventListener('change', searchShows);
});

function loadShowSection(filter = 'all') {
    // Show the show section
    const section = document.getElementById('show-section');
    section.style.display = 'block';

    // Scroll to the section
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });

    // Set filter if specified
    if (filter !== 'all') {
        document.getElementById('status-filter').value = filter;
    }

    // Load shows
    loadShows(1);
}

function closeShowSection() {
    const section = document.getElementById('show-section');
    section.style.display = 'none';
}

function loadShows(page = 1) {
    const loadingDiv = document.getElementById('loading-shows');
    const contentDiv = document.getElementById('shows-content');

    // Show loading, hide content
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // Get filter values
    const search = document.getElementById('search-shows').value;
    const statusFilter = document.getElementById('status-filter').value;
    const perPage = document.getElementById('per-page-shows').value;

    // Build URL parameters
    const params = new URLSearchParams({
        page: page,
        per_page: perPage,
        search: search,
        status_filter: statusFilter
    });

    // Make AJAX request
    fetch(`<?php echo BASE_URL; ?>/coordinator/loadShows?` + params.toString(), {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderShows(data);
        } else {
            showShowError(data.error || 'Failed to load shows');
        }
    })
    .catch(error => {
        console.error('Error loading shows:', error);
        showShowError('Network error occurred');
    });
}

function searchShows() {
    loadShows(1);
}

function clearShowSearch() {
    document.getElementById('search-shows').value = '';
    document.getElementById('status-filter').value = 'all';
    loadShows(1);
}

function renderShows(data) {
    const loadingDiv = document.getElementById('loading-shows');
    const contentDiv = document.getElementById('shows-content');

    // Hide loading
    loadingDiv.style.display = 'none';

    // Render shows table and pagination
    let html = '';

    if (data.shows.length === 0) {
        html = '<div class="card-body text-center"><p class="text-muted">No shows found.</p></div>';
    } else {
        html = renderShowsTable(data.shows, data.pagination);
    }

    contentDiv.innerHTML = html;
    contentDiv.style.display = 'block';

    // Update show count display
    document.getElementById('show-count-display').textContent = data.pagination.total_shows.toLocaleString();
}

function renderShowsTable(shows, pagination) {
    let html = '<div class="table-responsive"><table class="table table-striped table-hover mb-0">';

    // Table header
    html += '<thead class="table-light"><tr>';
    html += '<th>Show Name</th><th>Date</th><th>Location</th><th>Status</th><th>Registrations</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    // Table rows
    shows.forEach(show => {
        html += '<tr>';
        html += '<td><strong>' + show.name + '</strong></td>';
        html += '<td>' + formatDate(show.start_date) + '</td>';
        html += '<td>' + (show.location || 'TBD') + '</td>';
        html += '<td>' + getStatusBadge(show) + '</td>';
        html += '<td><span class="badge bg-primary">' + (show.registration_count || 0) + '</span></td>';
        html += '<td>' + getShowActions(show.id) + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';

    // Pagination
    if (pagination.total_pages > 1) {
        html += renderShowPagination(pagination);
    }

    // Results info
    html += '<div class="card-footer text-muted small">';
    html += `Showing ${pagination.start_record}-${pagination.end_record} of ${pagination.total_shows.toLocaleString()} shows`;
    html += '</div>';

    return html;
}

function getStatusBadge(show) {
    const now = new Date();
    const startDate = new Date(show.start_date);

    if (show.status === 'draft') {
        return '<span class="badge bg-secondary">Draft</span>';
    } else if (startDate > now) {
        return '<span class="badge bg-success">Upcoming</span>';
    } else {
        return '<span class="badge bg-warning text-dark">Past</span>';
    }
}

function getShowActions(showId) {
    return `
        <div class="btn-group btn-group-sm">
            <a href="<?php echo BASE_URL; ?>/coordinator/show/${showId}" class="btn btn-info" title="View Show">
                <i class="fas fa-eye"></i>
            </a>
            <a href="<?php echo BASE_URL; ?>/coordinator/editShow/${showId}" class="btn btn-primary" title="Edit Show">
                <i class="fas fa-edit"></i>
            </a>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" title="Manage">
                    <i class="fas fa-cog"></i>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/registrations/${showId}">
                        <i class="fas fa-list me-2"></i>Registrations
                    </a></li>
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/categories/${showId}">
                        <i class="fas fa-tags me-2"></i>Categories
                    </a></li>
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/judges/${showId}">
                        <i class="fas fa-gavel me-2"></i>Judges
                    </a></li>
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/metrics/${showId}">
                        <i class="fas fa-chart-bar me-2"></i>Judging Metrics
                    </a></li>
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/age_weights/${showId}">
                        <i class="fas fa-weight me-2"></i>Age Weights
                    </a></li>
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/manageStaff/${showId}">
                        <i class="fas fa-users me-2"></i>Staff (Legacy)
                    </a></li>
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/show_roles/manage/${showId}">
                        <i class="fas fa-user-tag me-2"></i>Role Assignments
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/fanVotes/${showId}">
                        <i class="fas fa-heart me-2"></i>Fan Votes
                    </a></li>
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/qrCodes/${showId}">
                        <i class="fas fa-qrcode me-2"></i>QR Codes
                    </a></li>
                </ul>
            </div>
        </div>
    `;
}

function renderShowPagination(pagination) {
    let html = '<nav class="mt-3"><ul class="pagination pagination-sm justify-content-center">';

    // Previous button
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadShows(${pagination.current_page - 1})">Previous</a></li>`;
    }

    // Page numbers (simplified)
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const active = i === pagination.current_page ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadShows(${i})">${i}</a></li>`;
    }

    // Next button
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadShows(${pagination.current_page + 1})">Next</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

function showShowError(message) {
    const loadingDiv = document.getElementById('loading-shows');
    const contentDiv = document.getElementById('shows-content');

    loadingDiv.style.display = 'none';
    contentDiv.innerHTML = `<div class="card-body text-center"><div class="alert alert-danger">${message}</div></div>`;
    contentDiv.style.display = 'block';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function exportShows() {
    // Get current filter values
    const search = document.getElementById('search-shows').value;
    const statusFilter = document.getElementById('status-filter').value;

    // Build export URL with filters
    const params = new URLSearchParams({
        search: search,
        status_filter: statusFilter,
        export: 'csv'
    });

    const exportUrl = `<?php echo BASE_URL; ?>/coordinator/exportShows?` + params.toString();

    // Open export URL in new window
    window.open(exportUrl, '_blank');
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
