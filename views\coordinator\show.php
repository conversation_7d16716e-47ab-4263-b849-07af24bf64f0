<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/coordinator/dashboard">Coordinator Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><?php echo $data['show']->name; ?></li>
                </ol>
            </nav>
            
            <h1><?php echo $data['title']; ?></h1>
            
            <?php flash('coordinator_message'); ?>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Show Details</h5>
                        </div>
                        <div class="card-body">
                            <p><strong>Date:</strong> <?php echo isset($data['show']->start_date) ? formatDateTimeForUser($data['show']->start_date, $_SESSION['user_id'] ?? null, 'F j, Y') : 'Not set'; ?></p>
                            <p><strong>Venue:</strong> <?php echo $data['show']->location; ?></p>
                            <p><strong>Description:</strong> <?php echo $data['show']->description; ?></p>
                            <p>
                                <strong>Status:</strong> 
                                <?php if (isset($data['show']->status) && $data['show']->status == 'published') : ?>
                                    <span class="badge bg-success">Active</span>
                                <?php elseif (isset($data['show']->status) && $data['show']->status == 'payment_pending') : ?>
                                    <span class="badge bg-warning">Payment Pending</span>
                                <?php else : ?>
                                    <span class="badge bg-danger">Inactive</span>
                                <?php endif; ?>
                            </p>
                            
                            <p>
                                <strong>Listing Fee:</strong> 
                                <?php if (isset($data['show']->listing_paid) && $data['show']->listing_paid) : ?>
                                    <span class="badge bg-success">Paid</span>
                                <?php else : ?>
                                    <span class="badge bg-danger">Unpaid</span>
                                    <?php if (isset($data['show']->listing_fee) && $data['show']->listing_fee > 0) : ?>
                                        <a href="<?php echo URLROOT; ?>/payment/showListing/<?php echo $data['show']->id; ?>" class="btn btn-sm btn-primary ml-2">
                                            <i class="fas fa-credit-card"></i> Pay Now
                                        </a>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </p>
                            
                            <div class="mt-3">
                                <a href="<?php echo URLROOT; ?>/coordinator/editShow/<?php echo $data['show']->id; ?>" class="btn btn-secondary">
                                    <i class="fas fa-edit"></i> Edit Show
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Registration Statistics</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $totalRegistrations = 0;
                            foreach ($data['registration_counts'] as $count) {
                                $totalRegistrations += $count->count;
                            }
                            ?>
                            
                            <h4 class="text-center"><?php echo $totalRegistrations; ?> Total Registrations</h4>
                            
                            <?php if (!empty($data['registration_counts'])) : ?>
                                <div class="mt-3">
                                    <h6>Registrations by Category:</h6>
                                    <ul class="list-group">
                                        <?php foreach ($data['registration_counts'] as $count) : ?>
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <?php echo isset($count->category_name) ? $count->category_name : 'Uncategorized'; ?>
                                                <span class="badge bg-primary rounded-pill"><?php echo $count->count; ?></span>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <div class="mt-3 text-center">
                                <a href="<?php echo URLROOT; ?>/coordinator/registrations/<?php echo $data['show']->id; ?>" class="btn btn-primary">
                                    <i class="fas fa-list"></i> View All Registrations
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Categories</h5>
                            <a href="<?php echo URLROOT; ?>/coordinator/categories/<?php echo $data['show']->id; ?>" class="btn btn-light btn-sm">
                                <i class="fas fa-cog"></i> Manage
                            </a>
                        </div>
                        <div class="card-body">
                            <?php if (empty($data['categories'])) : ?>
                                <div class="alert alert-info">
                                    No categories have been created for this show yet.
                                </div>
                            <?php else : ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Fee</th>
                                                <th>Max Entries</th>
                                                <th>Current Entries</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($data['categories'] as $category) : ?>
                                                <?php
                                                $currentEntries = 0;
                                                foreach ($data['registration_counts'] as $count) {
                                                    if (isset($count->id) && $count->id == $category->id) {
                                                        $currentEntries = $count->count;
                                                        break;
                                                    }
                                                }
                                                ?>
                                                <tr>
                                                    <td><?php echo $category->name; ?></td>
                                                    <td>$<?php echo number_format($category->registration_fee, 2); ?></td>
                                                    <td><?php echo $category->max_entries > 0 ? $category->max_entries : 'Unlimited'; ?></td>
                                                    <td>
                                                        <?php echo $currentEntries; ?>
                                                        <?php if ($category->max_entries > 0 && $currentEntries >= $category->max_entries) : ?>
                                                            <span class="badge bg-danger">Full</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Judge Assignments</h5>
                            <a href="<?php echo URLROOT; ?>/judge_management/index/<?php echo $data['show']->id; ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-users-cog"></i> Judge Management
                            </a>
                        </div>
                        <div class="card-body">
                            <?php if (empty($data['judge_assignments'])) : ?>
                                <div class="alert alert-info">
                                    No judges have been assigned to this show yet.
                                </div>
                            <?php else : ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Judge</th>
                                                <th>Category</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($data['judge_assignments'] as $assignment) : ?>
                                                <tr>
                                                    <td><?php echo isset($assignment->judge_name) ? $assignment->judge_name : 'Unknown Judge'; ?></td>
                                                    <td>
                                                        <?php echo isset($assignment->category_name) ? $assignment->category_name : 'All Categories'; ?>
                                                    </td>
                                                    <td>
                                                        <?php if (isset($assignment->has_judged) && $assignment->has_judged) : ?>
                                                            <span class="badge bg-success">Active</span>
                                                        <?php else : ?>
                                                            <span class="badge bg-secondary">Pending</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Judging Metrics</h5>
                            <a href="<?php echo URLROOT; ?>/coordinator/metrics/<?php echo $data['show']->id; ?>" class="btn btn-light btn-sm">
                                <i class="fas fa-cog"></i> Manage
                            </a>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                Configure judging metrics to define how vehicles will be scored during the show.
                            </div>
                        </div>
                    </div>
                </div>
                
                <?php if (isset($data['show']->fan_voting_enabled) && $data['show']->fan_voting_enabled): ?>
                <div class="col-md-6">
                    <div class="card shadow-sm">
                        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Fan Favorite Voting</h5>
                            <div>
                                <a href="<?php echo URLROOT; ?>/coordinator/fanVotes/<?php echo $data['show']->id; ?>" class="btn btn-light btn-sm me-1">
                                    <i class="fas fa-chart-bar"></i> View Votes
                                </a>
                                <a href="<?php echo URLROOT; ?>/coordinator/qrCodes/<?php echo $data['show']->id; ?>" class="btn btn-light btn-sm">
                                    <i class="fas fa-qrcode"></i> QR Codes
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success">
                                <p><i class="fas fa-thumbs-up"></i> <strong>Fan Favorite Voting is enabled for this show!</strong></p>
                                <p>Generate QR codes for each vehicle to allow visitors to vote directly from their smartphones.</p>
                                <p>Monitor voting statistics and export results for your show.</p>
                            </div>
                            
                            <div class="text-center">
                                <a href="<?php echo URLROOT; ?>/show/vote/<?php echo $data['show']->id; ?>" class="btn btn-success" target="_blank">
                                    <i class="fas fa-external-link-alt"></i> View Public Voting Page
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Payment Management Section -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i> Payment Settings</h5>
                            <a href="<?php echo BASE_URL; ?>/payment/showSettings/<?php echo $data['show']->id; ?>" class="btn btn-light btn-sm">
                                <i class="fas fa-cog"></i> Configure
                            </a>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="icon-box bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                                    <i class="fas fa-dollar-sign text-primary"></i>
                                </div>
                                <div>
                                    <h5 class="mb-0">Registration Fee: <?php echo formatCurrency($data['show']->registration_fee); ?></h5>
                                    <p class="text-muted mb-0"><?php echo $data['show']->is_free ? 'Free Show' : 'Per Vehicle'; ?></p>
                                </div>
                            </div>
                            
                            <p class="mb-3">Configure which payment methods are available for this show and set up payment provider credentials.</p>
                            
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i> You can accept payments via PayPal, Venmo, CashApp, and other methods.
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i> Payment Statistics</h5>
                            <a href="<?php echo BASE_URL; ?>/payment/stats/<?php echo $data['show']->id; ?>" class="btn btn-light btn-sm">
                                <i class="fas fa-chart-bar"></i> View Details
                            </a>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="p-3">
                                        <h5 class="mb-0">Registration Fee</h5>
                                        <h3 class="mb-0"><?php echo formatCurrency($data['show']->registration_fee); ?></h3>
                                        <p class="text-muted mb-0">Per Vehicle</p>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="p-3">
                                        <h5 class="mb-0">Potential Revenue</h5>
                                        <?php
                                        $totalRegistrations = 0;
                                        foreach ($data['registration_counts'] as $count) {
                                            $totalRegistrations += $count->count;
                                        }
                                        $potentialRevenue = $totalRegistrations * $data['show']->registration_fee;
                                        ?>
                                        <h3 class="mb-0"><?php echo formatCurrency($potentialRevenue); ?></h3>
                                        <p class="text-muted mb-0">Based on <?php echo $totalRegistrations; ?> registrations</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Payment management buttons removed - coordinators should manage payments from the registration details page -->
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>