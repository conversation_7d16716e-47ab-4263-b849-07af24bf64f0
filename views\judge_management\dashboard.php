<?php require_once APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-gavel"></i> Judge Management</h2>
            <p class="text-muted mb-0"><?= htmlspecialchars($data['show']->name) ?></p>
            <small class="text-muted">
                <?= date('M j, Y', strtotime($data['show']->start_date)) ?> - 
                <?= date('M j, Y', strtotime($data['show']->end_date)) ?>
            </small>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= URLROOT ?>/<?= $data['is_admin'] ? 'admin' : 'coordinator' ?>/show/<?= $data['show']->id ?>" 
               class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Show
            </a>
        </div>
    </div>

    <?php flash('success'); ?>
    <?php flash('error'); ?>

    <!-- Quick Help Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info border-left-info">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="alert-heading mb-1"><i class="fas fa-info-circle"></i> How to Manage Judges</h6>
                        <small class="mb-0">
                            <?php if (!$data['is_admin']): ?>
                                <strong>Secure 3-Step Process:</strong><br>
                                <strong>1.</strong> Enter the User ID number of the person you want as a judge →
                                <strong>2.</strong> Choose categories (or "All Categories") →
                                <strong>3.</strong> Click "Assign Judge" → Done! They'll automatically get judge permissions.
                            <?php else: ?>
                                <strong>Simple 3-Step Process:</strong><br>
                                <strong>1.</strong> Search for any user →
                                <strong>2.</strong> Choose categories (or "All Categories") →
                                <strong>3.</strong> Click "Assign Judge" → Done! They'll automatically get judge permissions.
                            <?php endif; ?>
                        </small>
                    </div>
                    <div class="col-md-4 text-md-end mt-2 mt-md-0">
                        <a href="<?= URLROOT ?>/admin/judging/<?= $data['show']->id ?>" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-gavel"></i> Start Judging
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= count($data['judge_data']) ?></h4>
                            <p class="mb-0">Total Judges</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= count(array_filter($data['judge_data'], fn($j) => $j->status === 'approved')) ?></h4>
                            <p class="mb-0">Active Judges</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= count(array_filter($data['judge_data'], fn($j) => $j->status === 'pending')) ?></h4>
                            <p class="mb-0">Pending Approval</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <?php
                                $totalVehicles = 0;
                                foreach ($data['judging_progress'] as $progress) {
                                    $totalVehicles += $progress['total_to_judge'];
                                }
                            ?>
                            <h4><?= $totalVehicles ?></h4>
                            <p class="mb-0">Vehicles to Judge</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-car fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Judge Assignment Panel -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-user-plus"></i> Assign New Judge</h6>
                </div>
                <div class="card-body p-3">
                    <?php if ($data['is_admin']): ?>
                        <!-- Admin: AJAX User Search -->
                        <div class="alert alert-success alert-sm mb-3">
                            <small><i class="fas fa-search"></i> <strong>For Admins:</strong>
                            Search for any user below. They'll automatically get judge permissions when you assign categories!</small>
                        </div>

                        <form action="<?= URLROOT ?>/judge_management/assignJudge" method="POST">
                            <input type="hidden" name="show_id" value="<?= $data['show']->id ?>">
                            <input type="hidden" name="judge_id" id="selected_judge_id" value="">

                            <div class="mb-2">
                                <label for="judge_search" class="form-label small">Search for User</label>
                                <div class="position-relative">
                                    <input type="text" id="judge_search" class="form-control form-control-sm"
                                           placeholder="Type name, email, or phone to search..." autocomplete="off">
                                    <div id="search_results" class="position-absolute w-100 bg-white border border-top-0 rounded-bottom shadow-sm"
                                         style="display: none; z-index: 1000; max-height: 200px; overflow-y: auto;"></div>
                                </div>
                                <div id="selected_user" class="mt-2" style="display: none;">
                                    <div class="alert alert-info alert-sm mb-0">
                                        <strong>Selected:</strong> <span id="selected_user_info"></span>
                                        <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearSelection()">
                                            <i class="fas fa-times"></i> Clear
                                        </button>
                                    </div>
                                </div>
                            </div>
                    <?php elseif (!$data['is_admin']): ?>
                        <!-- Coordinator: Secure User ID Input -->
                        <div class="alert alert-info alert-sm mb-3">
                            <small><i class="fas fa-shield-alt"></i> <strong>For Security:</strong>
                            Enter the User ID number of the person you want to assign as a judge. You can find User IDs in the user management system.</small>
                        </div>

                        <form action="<?= URLROOT ?>/judge_management/assignJudge" method="POST">
                            <input type="hidden" name="show_id" value="<?= $data['show']->id ?>">

                            <div class="mb-2">
                                <label for="judge_id" class="form-label small">User ID Number</label>
                                <input type="number" name="judge_id" id="judge_id" class="form-control form-control-sm"
                                       placeholder="Enter User ID (e.g. 12345)" required min="1">
                                <small class="form-text text-muted">Enter the numeric User ID of the person to assign as judge</small>
                            </div>
                    <?php endif; ?>

                    <?php if ($data['is_admin'] || !$data['is_admin']): ?>
                            <!-- Categories section for both admin and coordinator -->
                            <div class="mb-2">
                                <label class="form-label small">Categories to Judge</label>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="all_categories" value="">
                                    <label class="form-check-label small fw-bold" for="all_categories">
                                        All Categories
                                    </label>
                                </div>
                                <hr class="my-1">
                                <?php foreach ($data['categories'] as $category): ?>
                                    <div class="form-check" style="margin: 0; padding: 0 0 0 1.25rem; height: 1.5rem; display: flex; align-items: center;">
                                        <input class="form-check-input category-checkbox" type="checkbox"
                                               name="category_ids[]" value="<?= $category->id ?>"
                                               id="cat_<?= $category->id ?>" style="margin: 0; position: relative; top: 0;">
                                        <label class="form-check-label small" for="cat_<?= $category->id ?>" style="margin-left: 0.25rem; font-size: 0.8rem;">
                                            <?= htmlspecialchars($category->name) ?>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <?php if (!$data['is_admin']): ?>
                                <div class="mb-2">
                                    <label for="assignment_type" class="form-label small">Assignment Type</label>
                                    <select name="assignment_type" id="assignment_type" class="form-select form-select-sm">
                                        <option value="request">Send Request (User Approval Required)</option>
                                        <option value="direct">Direct Assignment (Immediate)</option>
                                    </select>
                                </div>
                            <?php else: ?>
                                <input type="hidden" name="assignment_type" value="direct">
                            <?php endif; ?>

                            <button type="submit" class="btn btn-primary btn-sm w-100">
                                <i class="fas fa-plus"></i> Assign Judge
                            </button>
                        </form>
                    <?php else: ?>
                        <div class="alert alert-info alert-sm">
                            All available judges have been assigned to this show.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Current Judges -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5><i class="fas fa-list"></i> Current Judge Assignments</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($data['judge_data'])): ?>
                        <div class="alert alert-info">
                            No judges have been assigned to this show yet.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Judge</th>
                                        <th>Status</th>
                                        <th>Categories</th>
                                        <th>Progress</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['judge_data'] as $judge): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($judge->judge_name) ?></strong><br>
                                                    <small class="text-muted"><?= htmlspecialchars($judge->judge_email) ?></small><br>
                                                    <?php if (!empty($judge->judge_phone)): ?>
                                                        <small class="text-muted">
                                                            <i class="fas fa-phone"></i> <?= htmlspecialchars($judge->judge_phone) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($judge->status === 'approved'): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php elseif ($judge->status === 'pending'): ?>
                                                    <span class="badge bg-warning">Pending Approval</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Not Assigned</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($judge->assigned_categories): ?>
                                                    <small><?= htmlspecialchars($judge->assigned_categories) ?></small>
                                                <?php elseif ($judge->category_count == 0 && $judge->status === 'approved'): ?>
                                                    <span class="text-warning">No categories assigned</span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= $judge->category_count ?> categories</span>
                                            </td>
                                            <td>
                                                <?php if ($judge->status === 'approved'): ?>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-xs btn-outline-primary" style="font-size: 0.7rem; padding: 0.2rem 0.4rem;"
                                                                onclick="editJudgeCategories(<?= $judge->user_id ?>, '<?= htmlspecialchars($judge->judge_name) ?>')"
                                                                title="Edit Categories">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-xs btn-outline-info" style="font-size: 0.7rem; padding: 0.2rem 0.4rem;"
                                                                onclick="sendMessageToJudge(<?= $judge->user_id ?>, '<?= htmlspecialchars($judge->judge_name) ?>')"
                                                                title="Send Message">
                                                            <i class="fas fa-envelope"></i>
                                                        </button>
                                                        <button class="btn btn-xs btn-outline-danger" style="font-size: 0.7rem; padding: 0.2rem 0.4rem;"
                                                                onclick="removeJudge(<?= $judge->user_id ?>, '<?= htmlspecialchars($judge->judge_name) ?>')"
                                                                title="Remove Judge">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                <?php elseif ($judge->status === 'pending'): ?>
                                                    <small class="text-muted">Awaiting response</small>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Individual Judge Progress - Full Width -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-chart-bar"></i> Individual Judge Progress</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($data['judging_progress'])): ?>
                        <div class="alert alert-info">
                            No judges assigned yet.
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php $count = 0; ?>
                            <?php foreach ($data['judging_progress'] as $judgeId => $progress): ?>
                                <?php
                                    $percentage = $progress['total_to_judge'] > 0
                                        ? round(($progress['total_judged'] / $progress['total_to_judge']) * 100)
                                        : 0;
                                    $progressClass = $percentage == 100 ? 'bg-success' : ($percentage > 50 ? 'bg-warning' : 'bg-danger');
                                    $remaining = $progress['total_to_judge'] - $progress['total_judged'];
                                ?>
                                <div class="col-md-4 mb-4">
                                    <div class="card h-100 border-left-<?= $percentage == 100 ? 'success' : ($percentage > 50 ? 'warning' : 'danger') ?>">
                                        <div class="card-header bg-light">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0 fw-bold"><?= htmlspecialchars($progress['judge_name']) ?></h6>
                                                <button class="btn btn-xs btn-outline-primary" style="font-size: 0.7rem; padding: 0.2rem 0.4rem;"
                                                        onclick="sendMessageToJudge(<?= $judgeId ?>, '<?= htmlspecialchars($progress['judge_name']) ?>')"
                                                        title="Send Message">
                                                    <i class="fas fa-envelope"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <!-- Overall Progress -->
                                            <div class="mb-3">
                                                <div class="d-flex justify-content-between mb-1">
                                                    <small class="fw-bold">Overall Progress</small>
                                                    <small><?= $progress['total_judged'] ?>/<?= $progress['total_to_judge'] ?></small>
                                                </div>
                                                <div class="progress" style="height: 15px;">
                                                    <div class="progress-bar <?= $progressClass ?>"
                                                         style="width: <?= $percentage ?>%">
                                                        <?= $percentage ?>%
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Status Badges -->
                                            <div class="row text-center mb-3">
                                                <div class="col-4">
                                                    <div class="badge bg-success w-100 small"><?= $progress['total_judged'] ?><br>Complete</div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="badge bg-warning w-100 small"><?= $progress['total_draft'] ?><br>Draft</div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="badge bg-danger w-100 small"><?= $remaining ?><br>Remaining</div>
                                                </div>
                                            </div>

                                            <!-- Category Breakdown -->
                                            <div class="mt-2">
                                                <small class="text-muted fw-bold">Categories:</small>
                                                <?php foreach ($progress['categories'] as $category): ?>
                                                    <div class="d-flex justify-content-between align-items-center mt-1 small">
                                                        <span><?= htmlspecialchars($category['category_name']) ?></span>
                                                        <span class="text-muted">
                                                            <?= $category['vehicles_judged'] ?>/<?= $category['vehicles_to_judge'] ?>
                                                            <?php if ($category['vehicles_draft'] > 0): ?>
                                                                <span class="text-warning">(<?= $category['vehicles_draft'] ?> draft)</span>
                                                            <?php endif; ?>
                                                        </span>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php
                                    $count++;
                                    // Add row break after every 3 cards
                                    if ($count % 3 == 0 && $count < count($data['judging_progress'])):
                                ?>
                                    </div><div class="row">
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Judge Categories Modal -->
<div class="modal fade" id="editJudgeModal" tabindex="-1" aria-labelledby="editJudgeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editJudgeModalLabel">Edit Judge Categories</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?= URLROOT ?>/judge_management/updateCategories" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="show_id" value="<?= $data['show']->id ?>">
                    <input type="hidden" name="judge_id" id="edit_judge_id">

                    <div class="mb-3">
                        <strong>Judge: <span id="edit_judge_name"></span></strong>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Categories to Judge</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_all_categories" value="">
                            <label class="form-check-label fw-bold" for="edit_all_categories">
                                All Categories
                            </label>
                        </div>
                        <hr class="my-2">
                        <div id="edit_categories_list">
                            <?php foreach ($data['categories'] as $category): ?>
                                <div class="form-check" style="margin: 0; padding: 0 0 0 1.25rem; height: 1.5rem; display: flex; align-items: center;">
                                    <input class="form-check-input edit-category-checkbox" type="checkbox"
                                           name="category_ids[]" value="<?= $category->id ?>"
                                           id="edit_cat_<?= $category->id ?>" style="margin: 0; position: relative; top: 0;">
                                    <label class="form-check-label small" for="edit_cat_<?= $category->id ?>" style="margin-left: 0.25rem; font-size: 0.8rem;">
                                        <?= htmlspecialchars($category->name) ?>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Categories</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Send Message Modal -->
<div class="modal fade" id="messageJudgeModal" tabindex="-1" aria-labelledby="messageJudgeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="messageJudgeModalLabel">Send Message to Judge</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?= URLROOT ?>/judge_management/sendMessage" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="show_id" value="<?= $data['show']->id ?>">
                    <input type="hidden" name="judge_id" id="message_judge_id">

                    <div class="mb-3">
                        <strong>To: <span id="message_judge_name"></span></strong>
                    </div>

                    <div class="mb-3">
                        <label for="message_subject" class="form-label">Subject</label>
                        <input type="text" class="form-control" id="message_subject" name="subject"
                               placeholder="Optional subject line">
                    </div>

                    <div class="mb-3">
                        <label for="message_content" class="form-label">Message *</label>
                        <textarea class="form-control" id="message_content" name="message" rows="5"
                                  placeholder="Type your message here..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Send Message</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Handle "All Categories" checkbox
document.getElementById('all_categories').addEventListener('change', function() {
    const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
    categoryCheckboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
        checkbox.disabled = this.checked;
    });
});

// Handle individual category checkboxes
document.querySelectorAll('.category-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const allCategoriesCheckbox = document.getElementById('all_categories');
        const checkedCategories = document.querySelectorAll('.category-checkbox:checked');
        
        if (checkedCategories.length === 0) {
            allCategoriesCheckbox.checked = false;
        }
    });
});

function editJudgeCategories(judgeId, judgeName) {
    // Set judge info in modal
    document.getElementById('edit_judge_id').value = judgeId;
    document.getElementById('edit_judge_name').textContent = judgeName;

    // Clear existing selections
    document.getElementById('edit_all_categories').checked = false;
    document.querySelectorAll('.edit-category-checkbox').forEach(checkbox => {
        checkbox.checked = false;
        checkbox.disabled = false;
    });

    // Get current judge assignments
    fetch('<?= URLROOT ?>/judge_management/getJudgeDetails/<?= $data['show']->id ?>/' + judgeId, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.assignments) {
            // Check if judge is assigned to all categories (category_id is null)
            const hasAllCategories = data.assignments.some(assignment => assignment.category_id === null);

            if (hasAllCategories) {
                document.getElementById('edit_all_categories').checked = true;
                document.querySelectorAll('.edit-category-checkbox').forEach(checkbox => {
                    checkbox.checked = true;
                    checkbox.disabled = true;
                });
            } else {
                // Check specific categories
                data.assignments.forEach(assignment => {
                    if (assignment.category_id) {
                        const checkbox = document.getElementById('edit_cat_' + assignment.category_id);
                        if (checkbox) {
                            checkbox.checked = true;
                        }
                    }
                });
            }
        }
    })
    .catch(error => {
        console.error('Error loading judge details:', error);
        alert('Failed to load judge details');
    });

    // Show modal
    new bootstrap.Modal(document.getElementById('editJudgeModal')).show();
}

function sendMessageToJudge(judgeId, judgeName) {
    // Set judge info in modal
    document.getElementById('message_judge_id').value = judgeId;
    document.getElementById('message_judge_name').textContent = judgeName;

    // Clear form
    document.getElementById('message_subject').value = '';
    document.getElementById('message_content').value = '';

    // Show modal
    new bootstrap.Modal(document.getElementById('messageJudgeModal')).show();
}

function removeJudge(judgeId, judgeName) {
    if (confirm('Are you sure you want to remove ' + judgeName + ' from this show? This will remove their role assignment and all category assignments.')) {
        window.location.href = '<?= URLROOT ?>/judge_management/removeJudge/<?= $data['show']->id ?>/' + judgeId;
    }
}

// Handle "All Categories" checkbox in edit modal
document.getElementById('edit_all_categories').addEventListener('change', function() {
    const categoryCheckboxes = document.querySelectorAll('.edit-category-checkbox');
    categoryCheckboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
        checkbox.disabled = this.checked;
    });
});

// AJAX User Search for Admins
<?php if ($data['is_admin']): ?>
let searchTimeout;
let isSelectingUser = false; // Flag to prevent input event interference
const searchInput = document.getElementById('judge_search');
const searchResults = document.getElementById('search_results');
const selectedUserId = document.getElementById('selected_judge_id');
const selectedUserDiv = document.getElementById('selected_user');
const selectedUserInfo = document.getElementById('selected_user_info');

// Debug: Check if all elements are found
console.log('DOM Elements found:', {
    searchInput: !!searchInput,
    searchResults: !!searchResults,
    selectedUserId: !!selectedUserId,
    selectedUserDiv: !!selectedUserDiv,
    selectedUserInfo: !!selectedUserInfo
});

searchInput.addEventListener('input', function() {
    // Don't process input events when we're selecting a user
    if (isSelectingUser) {
        console.log('Ignoring input event during user selection');
        return;
    }

    const query = this.value.trim();

    // Clear previous timeout
    clearTimeout(searchTimeout);

    if (query.length < 2) {
        searchResults.style.display = 'none';
        return;
    }

    // Debounce search
    searchTimeout = setTimeout(() => {
        searchUsers(query);
    }, 300);
});

function searchUsers(query) {
    fetch('<?= URLROOT ?>/judge_management/searchUsers', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ query: query })
    })
    .then(response => response.json())
    .then(data => {
        displaySearchResults(data);
    })
    .catch(error => {
        console.error('Search error:', error);
        searchResults.style.display = 'none';
    });
}

function displaySearchResults(users) {
    console.log('Displaying search results:', users); // Debug log

    if (!users || users.length === 0) {
        searchResults.innerHTML = '<div class="p-2 text-muted small">No users found</div>';
        searchResults.style.display = 'block';
        return;
    }

    // Clear previous results
    searchResults.innerHTML = '';

    users.forEach((user, index) => {
        console.log('Creating result item for user:', user); // Debug log

        const resultItem = document.createElement('div');
        resultItem.className = 'search-result-item p-2 border-bottom';
        resultItem.style.cursor = 'pointer';

        resultItem.innerHTML = `
            <div class="small">
                <strong>${user.name}</strong> (${user.role})
                <br><span class="text-muted">${user.email}</span>
                ${user.phone ? `<br><span class="text-muted"><i class="fas fa-phone"></i> ${user.phone}</span>` : ''}
            </div>
        `;

        // Add hover effects
        resultItem.addEventListener('mouseover', function() {
            this.style.backgroundColor = '#f8f9fa';
        });

        resultItem.addEventListener('mouseout', function() {
            this.style.backgroundColor = 'white';
        });

        // Add click event
        resultItem.addEventListener('click', function(event) {
            console.log('Search result clicked for user:', user); // Debug log

            // Prevent any default behavior and event bubbling
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();

            selectUser(user.id, user.name, user.email, user.phone || '', user.role);
        });

        searchResults.appendChild(resultItem);
    });

    searchResults.style.display = 'block';
    console.log('Search results displayed, total items:', users.length); // Debug log
}

function selectUser(id, name, email, phone, role) {
    console.log('selectUser called with:', {id, name, email, phone, role}); // Debug log

    // Set flag to prevent input event interference
    isSelectingUser = true;

    // Check if elements exist
    if (!selectedUserId) {
        console.error('selectedUserId element not found!');
        isSelectingUser = false;
        return;
    }
    if (!selectedUserInfo) {
        console.error('selectedUserInfo element not found!');
        isSelectingUser = false;
        return;
    }
    if (!selectedUserDiv) {
        console.error('selectedUserDiv element not found!');
        isSelectingUser = false;
        return;
    }

    // Set the values
    selectedUserId.value = id;
    selectedUserInfo.innerHTML = `${name} (${email})${phone ? ` - ${phone}` : ''} - ${role}`;

    console.log('Before showing selectedUserDiv, current display:', selectedUserDiv.style.display);

    // Show the selected user div with EXTREME visual debugging
    selectedUserDiv.style.cssText = `
        display: block !important;
        background-color: red !important;
        border: 5px solid lime !important;
        padding: 20px !important;
        z-index: 999999 !important;
        position: relative !important;
        opacity: 1 !important;
        visibility: visible !important;
        transition: none !important;
        animation: none !important;
        transform: none !important;
        width: auto !important;
        height: auto !important;
        overflow: visible !important;
    `;

    // Also force the inner alert to be visible with extreme styling
    const innerAlert = selectedUserDiv.querySelector('.alert');
    if (innerAlert) {
        innerAlert.style.cssText = `
            display: block !important;
            background-color: yellow !important;
            border: 2px solid blue !important;
            opacity: 1 !important;
            visibility: visible !important;
            color: black !important;
            font-size: 16px !important;
            font-weight: bold !important;
            padding: 10px !important;
            margin: 5px !important;
            text-shadow: 1px 1px white !important;
        `;

        // Force all text elements to be visible
        const textElements = innerAlert.querySelectorAll('*');
        textElements.forEach(el => {
            el.style.cssText += `
                color: black !important;
                background-color: transparent !important;
                opacity: 1 !important;
                visibility: visible !important;
                display: inline !important;
            `;
        });

        console.log('Inner alert found and styled:', innerAlert);
        console.log('Inner alert innerHTML:', innerAlert.innerHTML);
        console.log('Inner alert computed styles:', window.getComputedStyle(innerAlert));
    } else {
        console.log('No inner alert found in selectedUserDiv');
        console.log('selectedUserDiv innerHTML:', selectedUserDiv.innerHTML);
    }

    console.log('After setting display block, current display:', selectedUserDiv.style.display);
    console.log('Element dimensions:', {
        width: selectedUserDiv.offsetWidth,
        height: selectedUserDiv.offsetHeight,
        top: selectedUserDiv.offsetTop,
        left: selectedUserDiv.offsetLeft
    });
    console.log('Element bounding rect:', selectedUserDiv.getBoundingClientRect());
    console.log('Parent element:', selectedUserDiv.parentElement);
    console.log('Parent display:', window.getComputedStyle(selectedUserDiv.parentElement).display);
    console.log('Element computed styles:', {
        display: window.getComputedStyle(selectedUserDiv).display,
        visibility: window.getComputedStyle(selectedUserDiv).visibility,
        opacity: window.getComputedStyle(selectedUserDiv).opacity,
        position: window.getComputedStyle(selectedUserDiv).position,
        zIndex: window.getComputedStyle(selectedUserDiv).zIndex
    });

    // Hide search results and clear search input
    searchResults.style.display = 'none';
    searchInput.value = '';

    console.log('User selected successfully. Hidden input value:', selectedUserId.value);
    console.log('Selected user info content:', selectedUserInfo.innerHTML);

    // Clear the flag after a brief delay
    setTimeout(() => {
        isSelectingUser = false;
        console.log('Selection process complete, flag cleared');
    }, 500);

    // Add multiple timeouts to catch when it disappears
    setTimeout(() => {
        console.log('After 100ms - selectedUserDiv display:', selectedUserDiv.style.display);
    }, 100);

    setTimeout(() => {
        console.log('After 200ms - selectedUserDiv display:', selectedUserDiv.style.display);
    }, 200);

    setTimeout(() => {
        console.log('After 500ms - selectedUserDiv display:', selectedUserDiv.style.display);
    }, 500);

    setTimeout(() => {
        console.log('After 1 second - selectedUserDiv display:', selectedUserDiv.style.display);
        console.log('After 1 second - selectedUserId value:', selectedUserId.value);
        console.log('After 1 second - selectedUserInfo content:', selectedUserInfo.innerHTML);
    }, 1000);

    // Monitor for changes to the element
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            console.log('MUTATION DETECTED:', mutation.type, mutation.attributeName);

            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                console.log('STYLE CHANGED on selectedUserDiv:', selectedUserDiv.style.display);
                console.log('New style value:', selectedUserDiv.getAttribute('style'));
                console.log('Stack trace:', new Error().stack);
            }

            if (mutation.type === 'childList') {
                console.log('CHILD NODES CHANGED');
                console.log('Added nodes:', mutation.addedNodes);
                console.log('Removed nodes:', mutation.removedNodes);
                console.log('Current innerHTML:', selectedUserDiv.innerHTML);
            }

            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                console.log('CLASS CHANGED:', selectedUserDiv.className);
            }
        });
    });

    observer.observe(selectedUserDiv, {
        attributes: true,
        childList: true,
        subtree: true,
        attributeOldValue: true
    });

    // Also check if there are multiple elements with the same ID
    const allSelectedUserDivs = document.querySelectorAll('#selected_user');
    console.log('Number of elements with ID "selected_user":', allSelectedUserDivs.length);
    if (allSelectedUserDivs.length > 1) {
        console.log('DUPLICATE IDs FOUND! Elements:', allSelectedUserDivs);
        allSelectedUserDivs.forEach((el, index) => {
            console.log(`Element ${index}:`, el);
            el.style.border = `10px solid ${index === 0 ? 'red' : 'purple'}`;
        });
    }
}

function clearSelection() {
    selectedUserId.value = '';
    selectedUserDiv.style.display = 'none';
    searchInput.value = '';
}

// Hide search results when clicking outside
document.addEventListener('click', function(e) {
    console.log('Document click detected, target:', e.target);

    if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
        console.log('Hiding search results due to outside click');
        searchResults.style.display = 'none';
    }

    // Check if the click might be affecting the selected user div
    if (selectedUserDiv && selectedUserDiv.style.display === 'block') {
        console.log('Selected user div is visible during click event');
    }
});

// Monitor all form submissions
document.addEventListener('submit', function(e) {
    console.log('Form submission detected:', e.target);
    console.log('Selected user div display at form submit:', selectedUserDiv ? selectedUserDiv.style.display : 'element not found');
});

// Monitor all AJAX requests
const originalFetch = window.fetch;
window.fetch = function(...args) {
    console.log('Fetch request:', args[0]);
    console.log('Selected user div state during fetch:', selectedUserDiv ? selectedUserDiv.style.display : 'not found');
    return originalFetch.apply(this, args).then(response => {
        console.log('Fetch response received for:', args[0]);
        console.log('Selected user div state after fetch:', selectedUserDiv ? selectedUserDiv.style.display : 'not found');
        return response;
    });
};

// Monitor page unload/reload
window.addEventListener('beforeunload', function() {
    console.log('PAGE IS BEING UNLOADED/REFRESHED!');
});

// Monitor hash changes
window.addEventListener('hashchange', function() {
    console.log('HASH CHANGED:', window.location.hash);
});

// Monitor history changes
const originalPushState = history.pushState;
history.pushState = function(...args) {
    console.log('HISTORY PUSH STATE:', args);
    return originalPushState.apply(this, args);
};

const originalReplaceState = history.replaceState;
history.replaceState = function(...args) {
    console.log('HISTORY REPLACE STATE:', args);
    return originalReplaceState.apply(this, args);
};

<?php endif; ?>
</script>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>
