<?php require_once APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-gavel"></i> Judge Management</h2>
            <p class="text-muted mb-0"><?= htmlspecialchars($data['show']->name) ?></p>
            <small class="text-muted">
                <?= date('M j, Y', strtotime($data['show']->start_date)) ?> - 
                <?= date('M j, Y', strtotime($data['show']->end_date)) ?>
            </small>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= URLROOT ?>/<?= $data['is_admin'] ? 'admin' : 'coordinator' ?>/show/<?= $data['show']->id ?>" 
               class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Show
            </a>
        </div>
    </div>

    <?php flash('success'); ?>
    <?php flash('error'); ?>

    <!-- Progress Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= count($data['judge_data']) ?></h4>
                            <p class="mb-0">Total Judges</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= count(array_filter($data['judge_data'], fn($j) => $j->status === 'approved')) ?></h4>
                            <p class="mb-0">Active Judges</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= count(array_filter($data['judge_data'], fn($j) => $j->status === 'pending')) ?></h4>
                            <p class="mb-0">Pending Approval</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?= $data['judging_progress']['total_vehicles'] ?></h4>
                            <p class="mb-0">Vehicles to Judge</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-car fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Judge Assignment Panel -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-user-plus"></i> Assign New Judge</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($data['available_judges'])): ?>
                        <form action="<?= URLROOT ?>/judge_management/assignJudge" method="POST">
                            <input type="hidden" name="show_id" value="<?= $data['show']->id ?>">
                            
                            <div class="mb-3">
                                <label for="judge_id" class="form-label">Select Judge</label>
                                <select name="judge_id" id="judge_id" class="form-select" required>
                                    <option value="">Choose a judge...</option>
                                    <?php foreach ($data['available_judges'] as $judge): ?>
                                        <option value="<?= $judge->id ?>">
                                            <?= htmlspecialchars($judge->name) ?> (<?= htmlspecialchars($judge->email) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Categories to Judge</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="all_categories" value="">
                                    <label class="form-check-label fw-bold" for="all_categories">
                                        All Categories
                                    </label>
                                </div>
                                <hr class="my-2">
                                <?php foreach ($data['categories'] as $category): ?>
                                    <div class="form-check">
                                        <input class="form-check-input category-checkbox" type="checkbox" 
                                               name="category_ids[]" value="<?= $category->id ?>" 
                                               id="cat_<?= $category->id ?>">
                                        <label class="form-check-label" for="cat_<?= $category->id ?>">
                                            <?= htmlspecialchars($category->name) ?>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <?php if (!$data['is_admin']): ?>
                                <div class="mb-3">
                                    <label for="assignment_type" class="form-label">Assignment Type</label>
                                    <select name="assignment_type" id="assignment_type" class="form-select">
                                        <option value="request">Send Request (User Approval Required)</option>
                                        <option value="direct">Direct Assignment (Immediate)</option>
                                    </select>
                                </div>
                            <?php else: ?>
                                <input type="hidden" name="assignment_type" value="direct">
                            <?php endif; ?>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-plus"></i> Assign Judge
                            </button>
                        </form>
                    <?php else: ?>
                        <div class="alert alert-info">
                            All available judges have been assigned to this show.
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Category Progress -->
            <div class="card mt-4">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-chart-bar"></i> Judging Progress</h5>
                </div>
                <div class="card-body">
                    <?php foreach ($data['judging_progress']['category_progress'] as $category): ?>
                        <?php 
                            $percentage = $category->vehicles_in_category > 0 
                                ? round(($category->vehicles_judged / $category->vehicles_in_category) * 100) 
                                : 0;
                            $progressClass = $percentage == 100 ? 'bg-success' : ($percentage > 50 ? 'bg-warning' : 'bg-danger');
                        ?>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-1">
                                <small class="fw-bold"><?= htmlspecialchars($category->category_name) ?></small>
                                <small><?= $category->vehicles_judged ?>/<?= $category->vehicles_in_category ?></small>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar <?= $progressClass ?>" 
                                     style="width: <?= $percentage ?>%"></div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Current Judges -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5><i class="fas fa-list"></i> Current Judge Assignments</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($data['judge_data'])): ?>
                        <div class="alert alert-info">
                            No judges have been assigned to this show yet.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Judge</th>
                                        <th>Status</th>
                                        <th>Categories</th>
                                        <th>Progress</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['judge_data'] as $judge): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($judge->judge_name) ?></strong><br>
                                                    <small class="text-muted"><?= htmlspecialchars($judge->judge_email) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($judge->status === 'approved'): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php elseif ($judge->status === 'pending'): ?>
                                                    <span class="badge bg-warning">Pending Approval</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Not Assigned</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($judge->assigned_categories): ?>
                                                    <small><?= htmlspecialchars($judge->assigned_categories) ?></small>
                                                <?php elseif ($judge->category_count == 0 && $judge->status === 'approved'): ?>
                                                    <span class="text-warning">No categories assigned</span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= $judge->category_count ?> categories</span>
                                            </td>
                                            <td>
                                                <?php if ($judge->status === 'approved'): ?>
                                                    <button class="btn btn-sm btn-outline-primary" 
                                                            onclick="editJudgeCategories(<?= $judge->user_id ?>)">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" 
                                                            onclick="removeJudge(<?= $judge->user_id ?>)">
                                                        <i class="fas fa-trash"></i> Remove
                                                    </button>
                                                <?php elseif ($judge->status === 'pending'): ?>
                                                    <small class="text-muted">Awaiting response</small>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Handle "All Categories" checkbox
document.getElementById('all_categories').addEventListener('change', function() {
    const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
    categoryCheckboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
        checkbox.disabled = this.checked;
    });
});

// Handle individual category checkboxes
document.querySelectorAll('.category-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const allCategoriesCheckbox = document.getElementById('all_categories');
        const checkedCategories = document.querySelectorAll('.category-checkbox:checked');
        
        if (checkedCategories.length === 0) {
            allCategoriesCheckbox.checked = false;
        }
    });
});

function editJudgeCategories(judgeId) {
    // Implementation for editing judge categories
    alert('Edit categories for judge ID: ' + judgeId);
}

function removeJudge(judgeId) {
    if (confirm('Are you sure you want to remove this judge from the show?')) {
        // Implementation for removing judge
        window.location.href = '<?= URLROOT ?>/judge_management/removeJudge/<?= $data['show']->id ?>/' + judgeId;
    }
}
</script>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>
