<?php require_once APPROOT . '/views/inc/header.php'; ?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-users-cog"></i> Show Role Manager - Admin Overview</h2>
                    <p class="text-muted mb-0">Manage all show role assignments across the system</p>
                </div>
                <div>
                    <a href="<?= URLROOT ?>/admin/dashboard" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Admin Dashboard
                    </a>
                </div>
            </div>

            <?php flash('success'); ?>
            <?php flash('error'); ?>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-2 col-sm-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3 class="mb-0"><?= $data['stats']['total_assignments'] ?></h3>
                            <small>Total Assignments</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-6 mb-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body text-center">
                            <h3 class="mb-0"><?= $data['stats']['total_pending'] ?></h3>
                            <small>Pending Requests</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3 class="mb-0"><?= $data['stats']['coordinator_assignments'] ?></h3>
                            <small>Coordinators</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3 class="mb-0"><?= $data['stats']['judge_assignments'] ?></h3>
                            <small>Judges</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-6 mb-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h3 class="mb-0"><?= $data['stats']['staff_assignments'] ?></h3>
                            <small>Staff</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-6 mb-3">
                    <div class="card bg-dark text-white">
                        <div class="card-body text-center">
                            <a href="<?= URLROOT ?>/admin/shows" class="text-white text-decoration-none">
                                <i class="fas fa-plus fa-2x mb-2"></i><br>
                                <small>Manage Shows</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Requests -->
            <?php if (!empty($data['all_pending_requests'])): ?>
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-clock"></i> Pending Role Assignment Requests</h5>
                        <small>These requests are waiting for user approval</small>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Show</th>
                                        <th>Role</th>
                                        <th>Requested By</th>
                                        <th>Requested</th>
                                        <th>Expires</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['all_pending_requests'] as $request): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($request->user_name) ?></strong><br>
                                                <small class="text-muted"><?= htmlspecialchars($request->user_email) ?></small>
                                            </td>
                                            <td>
                                                <strong><?= htmlspecialchars($request->show_name) ?></strong><br>
                                                <small class="text-muted"><?= date('M j, Y', strtotime($request->start_date)) ?></small>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?= getRoleBadgeClass($request->requested_role) ?>">
                                                    <?= ucfirst($request->requested_role) ?>
                                                </span>
                                            </td>
                                            <td><?= htmlspecialchars($request->requested_by_name) ?></td>
                                            <td><?= date('M j, Y g:i A', strtotime($request->requested_at)) ?></td>
                                            <td>
                                                <span class="text-danger"><?= date('M j, Y g:i A', strtotime($request->expires_at)) ?></span>
                                            </td>
                                            <td>
                                                <a href="<?= URLROOT ?>/show_roles/manage/<?= $request->show_id ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-cog"></i> Manage
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- All Active Assignments -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-users"></i> All Active Role Assignments</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($data['all_assignments'])): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-users-cog fa-3x mb-3"></i>
                            <p>No active role assignments found.</p>
                            <p>Role assignments will appear here when coordinators assign users to shows.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped" id="assignmentsTable">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Show</th>
                                        <th>Role</th>
                                        <th>Assigned By</th>
                                        <th>Show Date</th>
                                        <th>Assigned Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['all_assignments'] as $assignment): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($assignment->user_name) ?></strong><br>
                                                <small class="text-muted"><?= htmlspecialchars($assignment->user_email) ?></small>
                                            </td>
                                            <td>
                                                <strong><?= htmlspecialchars($assignment->show_name) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?= getRoleBadgeClass($assignment->assigned_role) ?>">
                                                    <?= ucfirst($assignment->assigned_role) ?>
                                                </span>
                                            </td>
                                            <td><?= htmlspecialchars($assignment->assigned_by_name) ?></td>
                                            <td>
                                                <?= date('M j, Y', strtotime($assignment->start_date)) ?>
                                                <?php if ($assignment->start_date !== $assignment->end_date): ?>
                                                    to <?= date('M j, Y', strtotime($assignment->end_date)) ?>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= date('M j, Y', strtotime($assignment->assigned_at)) ?></td>
                                            <td>
                                                <?php if (strtotime($assignment->expires_at) < time()): ?>
                                                    <span class="badge badge-secondary">Expired</span>
                                                <?php elseif (strtotime($assignment->start_date) > time()): ?>
                                                    <span class="badge badge-info">Upcoming</span>
                                                <?php elseif (strtotime($assignment->end_date) < time()): ?>
                                                    <span class="badge badge-success">Completed</span>
                                                <?php else: ?>
                                                    <span class="badge badge-primary">Active</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= URLROOT ?>/show_roles/manage/<?= $assignment->show_id ?>" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-cog"></i> Manage
                                                    </a>
                                                    <form action="<?= URLROOT ?>/show_roles/remove" method="POST" 
                                                          style="display: inline;" 
                                                          onsubmit="return confirm('Are you sure you want to remove this role assignment?')">
                                                        <input type="hidden" name="assignment_id" value="<?= $assignment->id ?>">
                                                        <input type="hidden" name="show_id" value="<?= $assignment->show_id ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                                            <i class="fas fa-times"></i> Remove
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
function getRoleBadgeClass($role) {
    switch ($role) {
        case 'coordinator': return 'primary';
        case 'judge': return 'success';
        case 'staff': return 'info';
        default: return 'secondary';
    }
}
?>

<!-- DataTables for better table management -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<script>
$(document).ready(function() {
    $('#assignmentsTable').DataTable({
        "pageLength": 25,
        "order": [[ 4, "desc" ]], // Sort by show date descending
        "columnDefs": [
            { "orderable": false, "targets": 7 } // Disable sorting on Actions column
        ]
    });
});
</script>

<?php require_once APPROOT . '/views/inc/footer.php'; ?>
