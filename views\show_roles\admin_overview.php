<?php require_once APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-users-cog"></i> Show Role Manager - Admin Overview</h2>
                    <p class="text-muted mb-0">Manage all show role assignments across the system</p>
                </div>
                <div>
                    <a href="<?= URLROOT ?>/admin/dashboard" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Admin Dashboard
                    </a>
                </div>
            </div>

            <?php flash('success'); ?>
            <?php flash('error'); ?>

            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="<?= URLROOT ?>/admin/shows" class="btn btn-primary btn-lg">
                                    <i class="fas fa-calendar-plus mb-2 d-block" style="font-size: 1.5rem;"></i>
                                    Manage Shows
                                    <small class="d-block">Create shows and assign coordinators</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <button type="button" class="btn btn-success btn-lg" data-bs-toggle="modal" data-bs-target="#quickAssignModal">
                                    <i class="fas fa-user-plus mb-2 d-block" style="font-size: 1.5rem;"></i>
                                    Quick Assign Role
                                    <small class="d-block">Assign user to any show</small>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="<?= URLROOT ?>/admin/users" class="btn btn-info btn-lg">
                                    <i class="fas fa-users mb-2 d-block" style="font-size: 1.5rem;"></i>
                                    Manage Users
                                    <small class="d-block">View and edit user accounts</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-2 col-sm-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3 class="mb-0"><?= $data['stats']['total_assignments'] ?></h3>
                            <small>Total Assignments</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-6 mb-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body text-center">
                            <h3 class="mb-0"><?= $data['stats']['total_pending'] ?></h3>
                            <small>Pending Requests</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3 class="mb-0"><?= $data['stats']['coordinator_assignments'] ?></h3>
                            <small>Coordinators</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3 class="mb-0"><?= $data['stats']['judge_assignments'] ?></h3>
                            <small>Judges</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-6 mb-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h3 class="mb-0"><?= $data['stats']['staff_assignments'] ?></h3>
                            <small>Staff</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-6 mb-3">
                    <div class="card bg-dark text-white">
                        <div class="card-body text-center">
                            <a href="<?= URLROOT ?>/admin/shows" class="text-white text-decoration-none">
                                <i class="fas fa-plus fa-2x mb-2"></i><br>
                                <small>Manage Shows</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Requests -->
            <?php if (!empty($data['all_pending_requests'])): ?>
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-clock"></i> Pending Role Assignment Requests</h5>
                        <small>These requests are waiting for user approval</small>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Show</th>
                                        <th>Role</th>
                                        <th>Requested By</th>
                                        <th>Requested</th>
                                        <th>Expires</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['all_pending_requests'] as $request): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($request->user_name) ?></strong><br>
                                                <small class="text-muted"><?= htmlspecialchars($request->user_email) ?></small>
                                            </td>
                                            <td>
                                                <strong><?= htmlspecialchars($request->show_name) ?></strong><br>
                                                <small class="text-muted"><?= date('M j, Y', strtotime($request->start_date)) ?></small>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?= getRoleBadgeClass($request->requested_role) ?>">
                                                    <?= ucfirst($request->requested_role) ?>
                                                </span>
                                            </td>
                                            <td><?= htmlspecialchars($request->requested_by_name) ?></td>
                                            <td><?= date('M j, Y g:i A', strtotime($request->requested_at)) ?></td>
                                            <td>
                                                <span class="text-danger"><?= date('M j, Y g:i A', strtotime($request->expires_at)) ?></span>
                                            </td>
                                            <td>
                                                <a href="<?= URLROOT ?>/show_roles/manage/<?= $request->show_id ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-cog"></i> Manage
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- All Active Assignments -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-users"></i> All Active Role Assignments</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($data['all_assignments'])): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-users-cog fa-3x mb-3"></i>
                            <p>No active role assignments found.</p>
                            <p>Role assignments will appear here when coordinators assign users to shows.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped" id="assignmentsTable">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Show</th>
                                        <th>Role</th>
                                        <th>Assigned By</th>
                                        <th>Show Date</th>
                                        <th>Assigned Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['all_assignments'] as $assignment): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($assignment->user_name) ?></strong><br>
                                                <small class="text-muted"><?= htmlspecialchars($assignment->user_email) ?></small>
                                            </td>
                                            <td>
                                                <strong><?= htmlspecialchars($assignment->show_name) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?= getRoleBadgeClass($assignment->assigned_role) ?>">
                                                    <?= ucfirst($assignment->assigned_role) ?>
                                                </span>
                                            </td>
                                            <td><?= htmlspecialchars($assignment->assigned_by_name) ?></td>
                                            <td>
                                                <?= date('M j, Y', strtotime($assignment->start_date)) ?>
                                                <?php if ($assignment->start_date !== $assignment->end_date): ?>
                                                    to <?= date('M j, Y', strtotime($assignment->end_date)) ?>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= date('M j, Y', strtotime($assignment->assigned_at)) ?></td>
                                            <td>
                                                <?php if (strtotime($assignment->expires_at) < time()): ?>
                                                    <span class="badge badge-secondary">Expired</span>
                                                <?php elseif (strtotime($assignment->start_date) > time()): ?>
                                                    <span class="badge badge-info">Upcoming</span>
                                                <?php elseif (strtotime($assignment->end_date) < time()): ?>
                                                    <span class="badge badge-success">Completed</span>
                                                <?php else: ?>
                                                    <span class="badge badge-primary">Active</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= URLROOT ?>/show_roles/manage/<?= $assignment->show_id ?>" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-cog"></i> Manage
                                                    </a>
                                                    <form action="<?= URLROOT ?>/show_roles/remove" method="POST" 
                                                          style="display: inline;" 
                                                          onsubmit="return confirm('Are you sure you want to remove this role assignment?')">
                                                        <input type="hidden" name="assignment_id" value="<?= $assignment->id ?>">
                                                        <input type="hidden" name="show_id" value="<?= $assignment->show_id ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                                            <i class="fas fa-times"></i> Remove
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Assign Modal -->
<div class="modal fade" id="quickAssignModal" tabindex="-1" aria-labelledby="quickAssignModalLabel" role="dialog" aria-modal="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickAssignModalLabel">
                    <i class="fas fa-user-plus"></i> Quick Assign Role
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?= URLROOT ?>/show_roles/assign" method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="show_id" class="form-label">Select Show</label>
                            <select name="show_id" id="show_id" class="form-select" required>
                                <option value="">Choose a show...</option>
                                <?php
                                // Get upcoming shows for the dropdown
                                $db = new Database();
                                $db->query('SELECT id, name, start_date FROM shows WHERE start_date >= CURDATE() ORDER BY start_date ASC LIMIT 20');
                                $upcomingShows = $db->resultSet();
                                foreach ($upcomingShows as $show):
                                ?>
                                    <option value="<?= $show->id ?>">
                                        <?= htmlspecialchars($show->name) ?> - <?= date('M j, Y', strtotime($show->start_date)) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">Role</label>
                            <select name="role" id="role" class="form-select" required>
                                <option value="">Choose a role...</option>
                                <option value="coordinator">Coordinator</option>
                                <option value="judge">Judge</option>
                                <option value="staff">Staff</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="user_search" class="form-label">Search User</label>
                        <input type="text" id="user_search" class="form-control" placeholder="Type user name or email to search...">
                        <input type="hidden" name="user_id" id="user_id" required>
                        <div id="user_search_results" class="mt-2"></div>
                    </div>
                    <div class="mb-3">
                        <label for="assignment_type" class="form-label">Assignment Type</label>
                        <select name="assignment_type" id="assignment_type" class="form-select" required>
                            <option value="direct">Direct Assignment (Immediate)</option>
                            <option value="request">Send Request (User Approval Required)</option>
                        </select>
                        <small class="form-text text-muted">
                            Direct assignments are immediate. Requests require user approval.
                        </small>
                    </div>
                    <div class="mb-3">
                        <label for="message" class="form-label">Message (Optional)</label>
                        <textarea name="message" id="message" class="form-control" rows="3"
                                  placeholder="Optional message to include with the assignment/request..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Assign Role
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
function getRoleBadgeClass($role) {
    switch ($role) {
        case 'coordinator': return 'primary';
        case 'judge': return 'success';
        case 'staff': return 'info';
        default: return 'secondary';
    }
}
?>

<!-- DataTables for better table management -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<script>
$(document).ready(function() {
    $('#assignmentsTable').DataTable({
        "pageLength": 25,
        "order": [[ 4, "desc" ]], // Sort by show date descending
        "columnDefs": [
            { "orderable": false, "targets": 7 } // Disable sorting on Actions column
        ]
    });
});

// User search functionality for Quick Assign modal
document.getElementById('user_search').addEventListener('input', function() {
    const query = this.value.trim();
    const resultsDiv = document.getElementById('user_search_results');

    if (query.length < 2) {
        resultsDiv.innerHTML = '';
        return;
    }

    fetch('<?= URLROOT ?>/show_roles/searchUsers', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'query=' + encodeURIComponent(query)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.users.length > 0) {
            let html = '<div class="list-group">';
            data.users.forEach(user => {
                html += `<button type="button" class="list-group-item list-group-item-action"
                                onclick="selectUser(${user.id}, '${user.name}', '${user.email}')">
                            <strong>${user.name}</strong><br>
                            <small class="text-muted">${user.email} - ID: ${user.id}</small>
                         </button>`;
            });
            html += '</div>';
            resultsDiv.innerHTML = html;
        } else {
            resultsDiv.innerHTML = '<div class="alert alert-info">No users found</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        resultsDiv.innerHTML = '<div class="alert alert-danger">Error searching users</div>';
    });
});

function selectUser(id, name, email) {
    document.getElementById('user_id').value = id;
    document.getElementById('user_search').value = name + ' (' + email + ')';
    document.getElementById('user_search_results').innerHTML = '';
}

// Force modal to appear above everything and fix accessibility
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('quickAssignModal');
    if (modal) {
        modal.addEventListener('show.bs.modal', function(e) {
            // Remove aria-hidden to fix accessibility warning
            modal.removeAttribute('aria-hidden');

            // Force modal and backdrop to highest z-index
            setTimeout(() => {
                const modalElement = document.querySelector('.modal.show');
                const backdrop = document.querySelector('.modal-backdrop');

                if (modalElement) {
                    modalElement.style.zIndex = '999999';
                    modalElement.style.position = 'fixed';
                    modalElement.style.top = '0';
                    modalElement.style.left = '0';
                    modalElement.style.width = '100vw';
                    modalElement.style.height = '100vh';
                    modalElement.removeAttribute('aria-hidden');
                }

                if (backdrop) {
                    backdrop.style.zIndex = '999998';
                    backdrop.style.position = 'fixed';
                    backdrop.style.top = '0';
                    backdrop.style.left = '0';
                    backdrop.style.width = '100vw';
                    backdrop.style.height = '100vh';
                }

                // Hide header elements
                const header = document.querySelector('.racing-header');
                if (header) {
                    header.style.visibility = 'hidden';
                    header.style.opacity = '0';
                    header.style.pointerEvents = 'none';
                }

                // Ensure modal content is focusable
                const modalContent = modalElement.querySelector('.modal-content');
                if (modalContent) {
                    modalContent.focus();
                }
            }, 50);
        });

        modal.addEventListener('hide.bs.modal', function() {
            // Restore aria-hidden when closing
            modal.setAttribute('aria-hidden', 'true');
        });

        modal.addEventListener('hidden.bs.modal', function() {
            // Restore header
            const header = document.querySelector('.racing-header');
            if (header) {
                header.style.visibility = 'visible';
                header.style.opacity = '1';
                header.style.pointerEvents = 'auto';
            }
        });
    }
});
</script>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>
